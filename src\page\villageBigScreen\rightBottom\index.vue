<!--
 * @Description: 右下-民情统计、信箱统计
 * @Author: chenz76
 * @Date: 2021-10-26 14:42:02
 * @LastEditors: chenz76
 * @LastEditTime: 2022-01-06 10:38:58
-->
<template>
  <div style="flex: 0 0 100%; position: relative; display: flex; flex-direction: column; padding: 1vh 1vw; padding-left: 0.5vw; box-sizing: border-box;">
    <div style="flex: 0 0 100%; position: relative; display: flex; flex-direction: column;">
      <img class="img-common" src="/img/bigScreen/modulebg.png" mode="scaleToFill" />

      <div style="display: flex; flex-direction: row; padding-right: 1vw;">
        <div class="head-idx" style="position: relative; width: 3vh; height: 3vh; font-weight: bold;">
          <img class="img-common" src="/img/bigScreen/idx.png" mode="scaleToFill" />
          06
        </div>
        <div style="position: relative; width: 100%; height: 100%; display: flex; align-items: center;">
          <img class="img-common" src="/img/bigScreen/subTitlebg.png" mode="scaleToFill" />
          <div class="head-title" style="margin-left: 1vw;">应用广场</div>
        </div>
      </div>
      <div class="content-grid">
        <div class="grid-item" v-for="(item, index) in gridData" :key="index">
          <img class="img-common" src="/img/bigScreen/grid.png" mode="scaleToFill" />
          <div class="grid-title">{{ item.label }}</div>
          <div style="display: flex; margin-top: 0.56vh;">
            <div class="grid-value">{{ item.value }}
            <span class="grid-unit">{{ item.unit }}</span></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from "echarts/lib/echarts";
import "echarts/lib/chart/bar";
import "echarts/lib/chart/pie";
import "echarts/lib/component/tooltip";
import "echarts/lib/component/title";
import "echarts/lib/component/legend";
import { debounce } from "lodash";
import { getPublicMind, getMailbox } from "@/api/screen/screen";
export default {
  data() {
    return {
      selectedType: 1,
      myChart1: null,
      myChart2: null,
      timer1: null,
      timer2: null,
      index1: 0,
      index2: 0,
      myChart1Data: [],
      myChart2Data: [],
      option1: {
        title: {
          show: false,
          text: "民情统计",
          x: "0",
        },
        tooltip: {
          trigger: "item",
          axisPointer: {
            type: "shadow",
            shadowStyle: {
              color: "rgba(0,46, 115, 0.3)",
            },
          },
          formatter: function (params) {
            var str = "";
            str += params.name + ": " + params.value;
            return str;
          },
          textStyle: {
            align: "center",
            color: "#5cc1ff",
            fontSize: 14,
          },
          backgroundColor: "rgba(15, 52, 135, 0.5)",
          borderWidth: "1",
          borderColor: "#5cc1ff",
          extraCssText: "box-shadow: 0 0 10px rgba(255, 255, 255, 0.7);",
        },
        color: [
          "#2A4379",
          "#0DECCF",
          "#2D9AF8",
          "#5470C6",
          "#EE6666",
          "#FAC858",
          "#91CC75",
        ],
        series: [
          {
            name: "民情统计",
            type: "pie",
            radius: ["50%", "70%"],
            center: ["50%", "50%"],
            avoidLabelOverlap: false,
            itemStyle: {
              // borderRadius: 4,
              // borderColor: '#fff',
              shadowBlur: 5,
              shadowColor: "#1ABBF5",
              borderWidth: 8,
              borderRadius: 3,
            },

            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 14,
                color: "white",
                itemHeight: 50,
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              { value: 0, name: "待处理" },
              { value: 0, name: "处理中" },
              { value: 0, name: "已办结" },
            ],
          },
        ],
        legend: {
          show: false,
          orient: "vertical", // 布局方式，默认为水平布局，可选为：'horizontal' ¦ 'vertical'
          left: "50%",
          y: "center",
          itemWidth: 30,
          itemHeight: 18,
          textStyle: {
            fontSize: 14,
            color: "#fff",
            verticalAlign: "top",
            align: "left",
            width: 90,
            padding: [0, 0, 0, 10],
          },
          itemGap: 20,
        },
      },
      option2: {
        title: {
          show: false,
          text: "信箱统计",
          x: "0",
        },
        tooltip: {
          trigger: "item",
          axisPointer: {
            type: "shadow",
            shadowStyle: {
              color: "rgba(0,46, 115, 0.3)",
            },
          },
          formatter: function (params) {
            var str = "";
            str += params.name + ": " + params.value;
            return str;
          },
          textStyle: {
            align: "center",
            color: "#5cc1ff",
            fontSize: 14,
          },
          backgroundColor: "rgba(15, 52, 135, 0.5)",
          borderWidth: "1",
          borderColor: "#5cc1ff",
          extraCssText: "box-shadow: 0 0 10px rgba(255, 255, 255, 0.7);",
        },
        color: [
          "#2A4379",
          "#0DECCF",
          "#2D9AF8",
          "#5470C6",
          "#EE6666",
          "#FAC858",
          "#91CC75",
        ],
        series: [
          {
            name: "信箱统计",
            type: "pie",
            radius: ["50%", "70%"],
            center: ["50%", "50%"],
            avoidLabelOverlap: false,
            itemStyle: {
              // borderRadius: 4,
              // borderColor: '#fff',
              shadowBlur: 5,
              shadowColor: "#1ABBF5",
              borderWidth: 8,
              borderRadius: 3,
            },

            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 14,
                color: "white",
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              { value: 0, name: "待回复" },
              { value: 0, name: "已回复" },
            ],
          },
        ],
        legend: {
          show: false,
          orient: "vertical", // 布局方式，默认为水平布局，可选为：'horizontal' ¦ 'vertical'
          left: "50%",
          y: "center",
          itemWidth: 30,
          itemHeight: 18,
          textStyle: {
            fontSize: 14,
            color: "#fff",
            verticalAlign: "top",
            align: "left",
            width: 90,
            padding: [0, 0, 0, 10],
          },
          itemGap: 20,
        },
      },
    };
  },
  computed: {},
  mounted() {
    // this.getPublicMind();
    // this.getMailbox();
    // this.setResize();
  },
  created() {
    // setTimeout(() => {
    //   this.handleAutoLoop1();
    //   this.handleAutoLoop2();
    // }, 500);
  },
  destroyed() {
    if (this.timer1) {
      clearInterval(this.timer1);
    }
    if (this.timer2) {
      clearInterval(this.timer2);
    }
    let self = this;
    window.removeEventListener("resize", function () {
      if (self.myChart1) {
        self.myChart1 = null;
      }
      if (self.myChart2) {
        self.myChart2 = null;
      }
    });
  },
  methods: {
    async getPublicMind() {
      const result = await new Promise((resolve) => {
        getPublicMind().then((res) => {
          if (res && res.data.code === 200) {
            resolve(res.data.data);
          }
        });
      });
      this.$nextTick(() => {
        if (result.length > 0) {
          this.option1.series[0].data = result;
          this.myChart1Data = result;
        }
        this.renderEcharts1();
      });
    },
    renderEcharts1() {
      this.option1.tooltip.formatter = (params) => {
        var percent = 0;
        var total = 0;
        for (var i = 0; i < this.myChart1Data.length; i++) {
          total += parseFloat(this.myChart1Data[i].value);
        }
        if (total === 0) {
          percent = 0;
        } else {
          percent = ((params.value / total) * 100).toFixed(0);
        }
        var str = "";
        str +=
          params.name + ": " + params.value + "<br>" + "占比：" + percent + "%";
        return str;
      };
      this.option1.series[0].emphasis.label.formatter = (params) => {
        var percent = 0;
        var total = 0;
        for (var i = 0; i < this.myChart1Data.length; i++) {
          total += parseFloat(this.myChart1Data[i].value);
        }
        if (total === 0) {
          percent = 0;
        } else {
          percent = ((params.value / total) * 100).toFixed(0);
        }
        var str = "";
        str +=
          params.name + ": " + params.value + "\n\n" + "占比：" + percent + "%";
        return str;
      };
      //将echarts存储起来，减少再次获取dom操作
      this.myChart1 = echarts.init(
        document.getElementById("echart-public-mind")
      );
      this.myChart1.setOption(this.option1);
    },
    async getMailbox() {
      const result = await new Promise((resolve) => {
        getMailbox().then((res) => {
          if (res && res.data.code === 200) {
            resolve(res.data.data);
          }
        });
      });
      this.$nextTick(() => {
        if (result.length > 0) {
          this.option2.series[0].data = result;
          this.myChart2Data = result;
        }
        this.renderEcharts2();
      });
    },
    renderEcharts2() {
      this.option2.tooltip.formatter = (params) => {
        var percent = 0;
        var total = 0;
        for (var i = 0; i < this.myChart2Data.length; i++) {
          total += parseFloat(this.myChart2Data[i].value);
        }
        if (total === 0) {
          percent = 0;
        } else {
          percent = ((params.value / total) * 100).toFixed(0);
        }
        var str = "";
        str +=
          params.name + ": " + params.value + "<br>" + "占比：" + percent + "%";
        return str;
      };
      this.option2.series[0].emphasis.label.formatter = (params) => {
        var percent = 0;
        var total = 0;
        for (var i = 0; i < this.myChart2Data.length; i++) {
          total += parseFloat(this.myChart2Data[i].value);
        }
        if (total === 0) {
          percent = 0;
        } else {
          percent = ((params.value / total) * 100).toFixed(0);
        }
        var str = "";
        str +=
          params.name + ": " + params.value + "\n\n" + "占比：" + percent + "%";
        return str;
      };
      //将echarts存储起来，减少再次获取dom操作
      this.myChart2 = echarts.init(document.getElementById("echart-mailbox"));
      this.myChart2.setOption(this.option2);
    },
    //自适应
    setResize() {
      let self = this;
      window.addEventListener(
        "resize",
        debounce(function () {
          if (self.myChart1) {
            self.myChart1.resize();
          }
          if (self.myChart2) {
            self.myChart2.resize();
          }
        }, 200)
      );
    },

    //开始自动轮播函数
    handleAutoLoop1() {
      if (this.timer1) {
        clearInterval(this.timer1);
      }
      this.timer1 = setInterval(() => {
        if (this.index1 > 0) {
          this.myChart1.dispatchAction({
            type: "downplay",
            seriesIndex: 0,
            dataIndex: this.index1 - 1,
          });
        }
        this.myChart1.dispatchAction({
          type: "highlight",
          seriesIndex: 0,
          dataIndex: this.index1,
        });
        this.index1 += 1;
        if (this.index1 >= this.myChart1Data.length) {
          this.index1 = 0;
          setTimeout(() => {
            this.myChart1.dispatchAction({
              type: "downplay",
              seriesIndex: 0,
              dataIndex: this.myChart1Data.length - 1,
            });
          }, 5000);
        }
      }, 5000);
    },
    //停止自动轮播函数
    handlePause1() {
      clearInterval(this.timer1);
      this.myChart1.dispatchAction({
        type: "downplay",
        seriesIndex: 0,
        dataIndex: this.index1 - 1,
      });
    },

    //开始自动轮播函数
    handleAutoLoop2() {
      if (this.timer2) {
        clearInterval(this.timer2);
      }
      this.timer2 = setInterval(() => {
        if (this.index2 > 0) {
          this.myChart2.dispatchAction({
            type: "downplay",
            seriesIndex: 0,
            dataIndex: this.index2 - 1,
          });
        }
        this.myChart2.dispatchAction({
          type: "highlight",
          seriesIndex: 0,
          dataIndex: this.index2,
        });
        this.index2 += 1;
        if (this.index2 >= this.myChart2Data.length) {
          this.index2 = 0;
          setTimeout(() => {
            this.myChart2.dispatchAction({
              type: "downplay",
              seriesIndex: 0,
              dataIndex: this.myChart2Data.length - 1,
            });
          }, 5000);
        }
      }, 5000);
    },
    //停止自动轮播函数
    handlePause2() {
      clearInterval(this.timer2);
      this.myChart2.dispatchAction({
        type: "downplay",
        seriesIndex: 0,
        dataIndex: this.index2 - 1,
      });
    },
    onTap(type) {
      this.selectedType = type;
    }
  },
};
</script>

<style lang="scss" scoped>

.img-common {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

.head-idx {
  text-shadow: 0 4px 8px #00000040, 0 8px 16px #00000040;
  font-size: 1.7vh;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  background: linear-gradient(188deg, #C9F5FF 11.62%, #00FFFC 30.26%, #00A1FF 94.12%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
}

.head-title {
  text-shadow: 0 4px 8px #00000040, 0 8px 16px #00000040;
  font-size: 1.5vh;
  font-style: normal;
  font-weight: Bold;
  line-height: normal;
  background: linear-gradient(188deg, #C9F5FF 11.62%, #00FFFC 30.26%, #00A1FF 94.12%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: flex;
  align-items: center;
}

.btn {
  position: relative;
  width: 6vw;
  height: 2.2vh;
  cursor: pointer;
  // display: flex;
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr); // 4列
  grid-template-rows: repeat(2, 1fr);    // 2行
  gap: 1.1vh 0.7vw;                          // 行间距和列间距
  padding: 0 1vw;
}

.grid-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  // width: 5.4vw;
  height: 8.5vh;
  position: relative;
}

.grid-title {
  color: #ffffff;
  text-align: center;
  font-size: 1.1vh;
  font-weight: normal;
}

.grid-value {
  text-shadow: 0 4px 8px #00000040, 0 8px 16px #00000040;
  font-size: 2.6vh;
  font-style: normal;
  font-weight: bold;
  line-height: normal;
  background: linear-gradient(188deg, #C9F5FF 11.62%, #00FFFC 30.26%, #00A1FF 94.12%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.grid-unit {
  text-shadow: 0 4px 8px #00000040, 0 8px 16px #00000040;
  font-size: 0.74vh;
  font-style: normal;
  font-weight: bold;
  line-height: normal;
  background: linear-gradient(188deg, #C9F5FF 11.62%, #00FFFC 30.26%, #00A1FF 94.12%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
</style>
