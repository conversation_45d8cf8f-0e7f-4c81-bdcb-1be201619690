<!--
 * @Description: 左上-领导班子
 * @Author: chenz76
 * @Date: 2021-10-26 14:42:02
 * @LastEditors: chenz76
 * @LastEditTime: 2023-06-06 09:52:16
-->
<template>
  <div style="flex: 0 0 100%; position: relative; display: flex; flex-direction: column; padding: 1vh 1vw; padding-right: 0.5vw; box-sizing: border-box;">
    <div style="flex: 0 0 100%; position: relative; display: flex; flex-direction: column;">
      <img class="img-common" src="/img/bigScreen/modulebg.png" mode="scaleToFill" />

      <HeaderView :idx="idx" :title="title"></HeaderView>

      <div class="foot-grid">
        <SparkGridItem v-for="(item, index) in dataList" :key="index" :data="item"></SparkGridItem>
      </div>
    </div>
  </div>
</template>
<script>
import HeaderView from '../common/headerView.vue';
import SparkGridItem from "../common/sparkGridItem.vue";
export default {
  name: 'EquipView',
  components: {
    HeaderView,
    SparkGridItem,
  },
  props: {
    idx: {
      type: String,
      default: "01",
    },
    title: {
      type: String,
      default: "设备信息",
    },
  },
  data() {
    return {
      dataList: [
        {
          label: '随手拍',
          value: '720',
          valueKey: '',
          unit: '个',
        },
        {
          label: '随手拍',
          value: '720',
          valueKey: '',
          unit: '个',
        },
        {
          label: '随手拍',
          value: '720',
          valueKey: '',
          unit: '个',
        },

      ],
    };
  },
  created() {
  },
  methods: {
  },
};
</script>

<style lang="scss" scoped>

@import "../common/common.scss";

.foot-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr); // 4列
  grid-template-rows: repeat(1, 1fr);    // 2行
  gap: 0 2vw;                          // 行间距和列间距
  padding: 2vh 1.14vw;
}
</style>
