<!--
 * @Description: 右下-民情统计、信箱统计
 * @Author: chenz76
 * @Date: 2021-10-26 14:42:02
 * @LastEditors: chenz76
 * @LastEditTime: 2022-01-06 10:38:58
-->
<template>
  <div class="foot-item">
    <img class="img-common" src="/img/bigScreen/footBg.png" mode="scaleToFill" />
    <div style="display: flex;">
      <div class="data-value">{{ data.value }}
      <span class="data-unit">{{ data.unit }}</span></div>
    </div>
    <div class="data-title" style="margin-top: 1.3vh;">{{ data.label }}</div>
  </div>
</template>
<script>
export default {
  name: "SparkGridItem",
  props: {
    data: Object,
  },
  data() {
    return {
    };
  },
  computed: {},
  mounted() {
  },
  created() {
  },
  destroyed() {
  },
  methods: {
  },
};
</script>

<style lang="scss" scoped>

@import './common.scss';

.data-title {
  color: #ffffff;
  text-align: center;
  font-size: 1.1vh;
  font-weight: normal;
}

.data-value {
  font-size: 2.6vh;
  font-style: normal;
  font-weight: bold;
  line-height: normal;
  background: linear-gradient(188deg, #C9F5FF 11.62%, #00FFFC 30.26%, #00A1FF 94.12%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.data-unit {
  text-shadow: 0 4px 8px #00000040, 0 8px 16px #00000040;
  font-size: 0.74vh;
  font-style: normal;
  font-weight: bold;
  line-height: normal;
  background: linear-gradient(188deg, #C9F5FF 11.62%, #00FFFC 30.26%, #00A1FF 94.12%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.foot-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  // width: 5.4vw;
  height: 10.4vh;
  position: relative;
}
</style>
