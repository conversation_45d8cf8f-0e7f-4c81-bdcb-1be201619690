<template>
  <el-row>
    <el-col :span="24">
      <basic-container>
        <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" :search.sync="query"
          :permission="permissionList" v-model="form" ref="crud" :before-open="beforeOpen" :upload-before="uploadBefore"
          :upload-after="uploadAfter" :upload-delete="uploadDelete" :upload-exceed="uploadExceed"
          :upload-preview="uploadPreview" @row-update="rowUpdate" @row-save="rowSave" @row-del="rowDel"
          @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange"
          @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
          <!-- 左侧菜单按钮 -->
          <template slot="menuLeft">
            <el-button type="primary" size="small" icon="el-icon-user" plain @click="handleGroup"
              v-if="permissionList.groupBtn">
              人员分组
            </el-button>
            <el-button type="danger" size="small" icon="el-icon-delete" plain @click="handleDelete"
              v-if="permissionList.delBtn" style="margin-left: 6px;">
              批量删除
            </el-button>
          </template>

          <!-- 行操作按钮 -->
          <template slot-scope="{ type, size, row, index }" slot="menu">
            <el-button :type="type" :size="size" @click.stop="$refs.crud.rowView(row, index)">
              <i class="el-icon-view"></i>
              查看
            </el-button>
            <el-button :type="type" :size="size" @click="$refs.crud.rowEdit(row, index)"
              v-if="permissionList.editBtn && row.status != 2">
              <i class="el-icon-edit"></i>
              编辑
            </el-button>
            <el-button :type="type" :size="size" @click="rowDel(row)" v-if="permissionList.delBtn">
              <i class="el-icon-delete"></i>
              删除
            </el-button>
          </template>
          <!-- 派发负责人按钮 -->
          <template slot-scope="{}" slot="distributeForm">
            <el-button size="mini" @click="openGroup"> 选择人员 </el-button>
          </template>
          <!-- 处理表格 -->
          <template slot-scope="{}" slot="tableForm">
            <avue-crud :option="dealOption" :data="dealData" ref="dealCrud" :before-open="dealBeforeOpen">
            </avue-crud>
          </template>
        </avue-crud>
        <!-- 人员分组按钮弹窗 -->
        <el-dialog title="人员分组管理" :visible.sync="groupVisible" width="50%" :before-close="handleClose"
          :append-to-body="true" :close-on-click-modal="false">
          <group />
        </el-dialog>
        <!-- 选择人员 -->
        <el-dialog title="选择人员" :visible.sync="selectGroupVisible" width="40%"
          :before-close="() => { selectGroupVisible = false }" :append-to-body="true" :close-on-click-modal="false">
          <person-select ref="personSelect" :type="false" @confirm="e => groupConfirm(e)" :existList=existList :maxlength="20" />
        </el-dialog>
      </basic-container>
    </el-col>
  </el-row>
</template>

<script>
import { mapGetters } from "vuex";
import { validateFile } from "@/views/components/util";
// 审核状态
import * as funList from "@/api/digitalGovernance/work.js";
import Group from "./components/group.vue"; // 引入子组件
import { downloadFileBlob } from '@/util/util';
import PersonSelect from "./components/personSelect.vue";
export default {
  components: {
    Group, PersonSelect
  },
  data () {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        selection: true,
        labelWidth: 120,
        dialogWidth: 1200,
        dialogClickModal: false,
        viewBtn: false,
        delBtn: false, // 默认是有删除、编辑按钮
        editBtn: false,
        column: [
          {
            label: "工作名称",
            prop: "name",
            searchSpan: 6,
            span: 12,
            search: true,
            maxlength: 100,
            showWordLimit: true,
            rules: [
              {
                required: true,
                whitespace: true,
                message: "请输入工作名称",
                trigger: ["blur", "change"],
              },
            ],
          }, {
            label: "工作状态",
            prop: "status",
            dataType: "number",
            search: true,
            searchSpan: 6,
            value: 0,
            span: 12,
            type: "select",
            dicData: [
              { label: "未开始", value: 0 },
              { label: "进行中", value: 1 },
              { label: "已完成", value: 2 },
            ],
            addDisabled: true,
            filterable: true,
            formslot: true,
            rules: [
              {
                required: true,
                message: "请选择工作状态",
                trigger: ["blur", "change"],
              },
            ],
          }, {
            label: "工作类型",
            prop: "type",
            dataType: "string",
            search: true,
            searchSpan: 6,
            span: 12,
            type: "select",
            dicUrl: `/api/blade-system/dict/dictionary?code=workType`,
            props: {
              //对应select的属性
              label: "dictValue",
              value: "dictKey",
            },
            filterable: true,
            rules: [
              {
                required: true,
                message: "请选择工作类型",
                trigger: ["blur", "change"],
              },
            ],
          }, {
            label: "时间范围",
            prop: "releaseTime",
            dataType: "array",
            hide: true,
            slot: true,
            span: 12,
            type: "daterange",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            rules: [
              {
                required: true,
                message: "请选择时间范围",
                trigger: ["blur", "change"],
              },
            ],
          }, {
            label: "负责人",
            prop: "distribute",
            viewDisplay: false,
            formslot: true,
            span: 12,
            rules: [{ required: true, message: "请选择负责人" }],
            hide: true,
          }, {
            label: "负责人姓名",
            prop: "userNames",
            span: 24,
            addDisabled: true,
            editDisabled: true,
            rules: [{ required: true, message: "请选择负责人" }],
          }, {
            label: "工作描述",
            prop: "workDesc",
            type: "textarea",
            hide: true,
            span: 24,
            maxlength: 500,
            showWordLimit: true,
          },
          {
            label: "附件",
            prop: "fileList",
            dataType: "object",
            slot: true,
            type: "upload",

            // listType: "picture",
            limit: 10,
            // accept: ".jpeg,.jpg,.png",
            span: 24,
            propsHttp: {
              res: "data",
              url: "link",
              name: "originalName",
            },
            props: {
              //对应upload的属性
              url: "link",
              name: "originalName",
            },
            tip: "仅支持上传图片、word、pdf文件,最多只能上传10个文件，单个文件不超过10MB",
            action: "/api/blade-resource/oss/endpoint/put-file-attach",
            hide: true,
            showColumn: false, //不出现显隐面板中
          },
          // 处理表格
          {
            prop: "table",
            labelWidth: 0,
            editDisplay: false,
            addDisplay: false,
            formslot: true,
            span: 24,
            hide: true,
          }
        ],
      },
      dealData: [],
      dealOption: {
        border: true,
        index: true,
        labelWidth: 120,
        dialogWidth: 900,
        dialogClickModal: false,
        delBtn: false, // 默认是有删除、编辑按钮
        editBtn: false,
        viewBtn: true,
        header: false,
        column: [
          { label: "工作名称", prop: "name" },
          {
            label: "工作状态",
            prop: "status",
            dataType: "number",
            span: 12,
            type: "select",
            dicData: [
              { label: "未开始", value: 0 },
              { label: "进行中", value: 1 },
              { label: "已完成", value: 2 },
            ],
          },
          { label: "负责人", prop: "userName", type: "textarea", span: 24 },
          { label: "执行情况", prop: "progress", type: "textarea", span: 24, hide: true },
        ]
      },
      data: [],
      existList: [],
      groupVisible: false, // 人员分组弹窗
      selectGroupVisible: false, // 选择人员
    };
  },
  watch: {
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    // 优化后的权限计算属性
    permissionList () {
      const prefix = "work";
      return Object.entries({
        addBtn: "_add",
        viewBtn: "_view",
        editBtn: "_edit",
        delBtn: "_delete",
        groupBtn: "_group",
      }).reduce((acc, [key, suffix]) => {
        acc[key] = this.vaildData(this.permission[`${prefix}${suffix}`], false);
        return acc;
      }, {});
    },
    ids () {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created () {
  },
  mounted () { },
  methods: {
    // 人员选择处理
    groupConfirm (data) {
      this.form.distribute = data.selectUsers // 为了过校验
      this.form.userNames = data.selectUsersName
      this.selectGroupVisible = false
      this.$nextTick(() => {
        this.$refs.crud.clearValidate(['distribute'])
        this.$refs.crud.clearValidate(['userNames'])
      })
    },
    openGroup () {
      this.selectGroupVisible = true
      this.$nextTick(() => {
        this.$refs.personSelect.checkedNode = this.existList
      })
    },
    // 人员分组开启关闭
    handleGroup () {
      this.groupVisible = true
    },
    handleClose () {
      this.groupVisible = false
    },
    // 新增
    rowSave (row, done, loading) {
      // console.log(row);
      if (!this.form.attachList) {
        this.form.attachList = [];
      }
      let submitData = {
        name: row.name,
        status: row.status,
        type: row.type,
        userIds: row.distribute,
        workDesc: row.workDesc,
        progress: row.progress,
        remark: row.remark,
        attachIds: row.attachList,
      };
      if (row.releaseTime) {
        if (Array.isArray(row.releaseTime)) {
          submitData.startDate = row.releaseTime[0];
          submitData.endDate = row.releaseTime[1];
        }
      }
      funList.save(submitData).then(
        async () => {
          this.$message.success(`成功新增`);
          await this.onLoad(this.page, this.query);
          done();
        },
        () => {
          loading();
        }
      );
    },
    // 修改
    rowUpdate (row, index, done, loading) {

      if (!this.form.attachList) {
        this.form.attachList = [];
      }
      let submitData = {
        id: row.id,
        name: row.name,
        status: row.status,
        type: row.type,
        userIds: row.distribute,
        workDesc: row.workDesc,
        progress: row.progress,
        remark: row.remark,
        attachIds: row.attachList,
      };
      if (row.releaseTime) {
        if (Array.isArray(row.releaseTime)) {
          submitData.startDate = row.releaseTime[0];
          submitData.endDate = row.releaseTime[1];
        }
      }
      funList.update(submitData).then(
        async () => {
          this.$message.success(`成功修改`);
          await this.onLoad(this.page, this.query);
          done();
        },
        () => {
          loading();
        }
      );
    },
    // 删除
    rowDel (row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return funList.remove({
            ids: row.id,
          });
        })
        .then(() => {
          this.onLoad(this.page, this.query);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    // 重置
    searchReset () {
      this.query = {};
      this.onLoad(this.page);
    },
    // 搜索
    searchChange (params, done) {
      this.page.currentPage = 1;
      this.query = params;
      this.onLoad(this.page, params);
      done();
    },
    // 勾选
    selectionChange (list) {
      this.selectionList = list;
    },
    // 重置勾选
    selectionClear () {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    // 多选删除
    handleDelete () {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return funList.remove({
            ids: this.ids,
          });
        })
        .then(() => {
          this.onLoad(this.page, this.query);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    // 查看预览
    // 打开前回调
    async beforeOpen (done, type) {
      if (type === "add") {
        this.form = {};
        this.existList = []
        done();
      }

      if (["edit", "view"].includes(type)) {
        funList
          .detail({
            id: this.form.id,
          })
          .then((res) => {
            this.form = res.data.data;
            this.form.distribute = res.data.data.userIds;
            this.dealData = res.data.data.dealList
            this.form.fileList = [];
            this.form.releaseTime = [this.form.startDate, this.form.endDate];
            if (res.data.data.attachList) {
              let tempAttachList = [];
              res.data.data.attachList.map((value) => {
                this.form.fileList.push({
                  label: value.originalName,
                  value: value.link,
                });
                tempAttachList.push(value.id);
              });
              this.form.attachList = tempAttachList;
            }
            // 编辑时负责人处理
            if (type == "edit") {
              const list = []
              for (let i = 0; i < res.data.data.userList.length; i++) {
                const element = res.data.data.userList[i];
                list[i] = {
                  id: element.id,
                  label: element.phone ? `${element.realName}-${element.phone}` : element.realName,
                  leaf: true
                }
              }
              this.existList = list
            }
            done();
          });
      }
    },
    // 上传前回调
    uploadBefore (file, done, loading, column) {
      // console.log({ file, done, loading, column });
      //文件个数可以在limit属性设置,超过则不会继续上传,也不会走这个函数,这个组件目前只能一次一个个传
      if (validateFile.call(this, "TypeIWP", 10, file)) {
        done();
      } else {
        loading();
      }
    },
    // 上传后执行操作
    uploadAfter (res, done, loading) {
      //fileList是form展示的[{label,value}],attachList是记录每次上传完的id
      if (!res || !res.attachId) {
        this.$message.error("上传失败");
        loading();
      } else {
        this.$message.success("上传成功");

        if (!this.form.attachList) {
          this.form.attachList = [];
          this.form.attachList.push(res.attachId);
        } else {
          this.form.attachList.push(res.attachId);
        }
        done();
      }
    },
    // 删除已上传文件
    uploadDelete (file, column) {
      // console.log({ file, column });

      return this.$confirm("是否确定移除该项？").then(() => {
        if (column.prop == "themePictureList") {
          this.form.themePicture = "";
          this.form.themePictureList.splice(file.uid, 1);
        } else {
          this.form.fileList.splice(file.uid, 1);
          this.form.attachList.splice(file.uid, 1);
        }
      });
    },
    // 上传限制
    uploadExceed (limit) {
      this.$message.error(`最多只能上传${limit}个文件`);
    },
    // 预览
    uploadPreview (file, column, done) {
      // console.log({ file, column, done });
      // done()
      if (file.type == "img") {
        done()
      } else {
        //url下载
        downloadFileBlob(file.url, file.name)
      }

    },
    // 当前页切换
    currentChange (currentPage) {
      this.page.currentPage = currentPage;
    },
    // 页面显示条数切换
    sizeChange (pageSize) {
      this.page.pageSize = pageSize;
    },
    // 刷新
    refreshChange () {
      this.onLoad(this.page, this.query);
    },
    // 首次加载
    async onLoad (page, params = {}) {
      this.loading = true;
      let query = {
        ...params,
        current: this.page.currentPage,
        size: this.page.pageSize,
      };

      let res = await funList.getList(query);
      if (res && res.data && res.data.success) {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        if (this.page.currentPage > 1 && this.page.currentPage > data.pages) {
          this.page.currentPage = data.pages
          this.onLoad()
        }
        this.selectionClear();
      }
    },
  },
};
</script>
<style scoped lang="scss">
.img-tiny {
  height: 100px;
  width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(245, 247, 250);
}

.btn-group-container {
  display: flex;
}
</style>
