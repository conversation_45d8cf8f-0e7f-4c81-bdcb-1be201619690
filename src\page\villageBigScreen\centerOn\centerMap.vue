<template>
  <div ref="mapChart" style="width: 100%; height: 100%;"></div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  mounted() {
    this.initChart();
  },
  methods: {
    initChart() {
      const chartDom = this.$refs.mapChart;
      const myChart = echarts.init(chartDom);


      const option = {
        backgroundColor: '#072f5f',
        title: {
          text: 'Brazil Map with Markers',
          left: 'center',
          textStyle: {
            color: '#fff'
          }
        },
        tooltip: {
          trigger: 'item'
        },
        geo: {
          map: 'Brazil',
          roam: true,
          itemStyle: {
            areaColor: '#006699',
            borderColor: '#fff'
          }
        },
        series: [
          {
            name: 'Markers',
            type: 'effectScatter',
            coordinateSystem: 'geo',
            data: [
              { name: 'Marker 1', value: [-43.1729, -22.9068] }, // Rio de Janeiro
              { name: 'Marker 2', value: [-46.6333, -23.5505] }, // Sao Paulo
              // 添加更多标记点
            ],
            symbolSize: function (val) {
              return val[2] * 10;
            },
            showEffectOn: 'render',
            rippleEffect: {
              brushType: 'stroke'
            },
            hoverAnimation: true,
            label: {
              formatter: '{b}',
              position: 'right',
              show: true
            },
            itemStyle: {
              color: 'orange' // 标记颜色
            },
            zlevel: 1
          }
        ]
      };


      myChart.setOption(option);

    }
  }
};
</script>

<style scoped>
/* ... existing code ... */
</style>
