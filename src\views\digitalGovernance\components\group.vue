<!-- 
/**
  * @Author: 林中奇
  * @Date: 2025/07/28
  * @lastAuthor:
  * @lastChangeDate:
  * @Explain: 工作管理-人员分组
  */
 
-->
<template>
  <el-row>
    <el-col :span="24">
      <basic-container>
        <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" :search.sync="query"
          v-model="form" ref="crud" :before-open="beforeOpen" @row-update="rowUpdate" @row-del="rowDel"
          @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange"
          @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad"
          :permission="permissionList">
          <!-- 左侧菜单按钮 -->
          <template slot="menuLeft">
            <el-button size="small" icon="el-icon-plus" plain @click="handleAdd(row, 'add')">
              新增
            </el-button>
            <el-button type="danger" size="small" icon="el-icon-delete" plain @click="handleDelete">
              删除
            </el-button>
          </template>
          <!-- 行操作按钮 -->
          <template slot-scope="{ type, size, row, index }" slot="menu">
            <el-button :type="type" :size="size" @click.stop="$refs.crud.rowView(row, index)"><i
                class="el-icon-view"></i> 查看</el-button>
            <el-button :type="type" :size="size" @click="handleAdd(row, 'edit')"><i class="el-icon-edit"></i>
              编辑</el-button>
            <el-button :type="type" :size="size" @click="rowDel(row)"><i class="el-icon-delete"></i> 删除</el-button>
          </template>
        </avue-crud>
        <el-dialog title="选择人员" :visible.sync="groupVisible" width="40%" :before-close="groupClose"
          :append-to-body="true" :close-on-click-modal="false">
          <person-select ref="personSelect" :type="true" @confirm="e => groupConfirm(e)" :existList="existList" />
        </el-dialog>

        <el-dialog title="人员分组新增" :visible.sync="groupSaveVisible" width="50%" :before-close="close"
          :append-to-body="true" :close-on-click-modal="false">
          <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px">
            <el-form-item label="分组名称" prop="name" required>
              <el-input size="mini" v-model.trim="ruleForm.name" placeholder="请输入分组名称" maxlength="30" />
            </el-form-item>
            <el-form-item label="负责人" prop="director" required>
              <el-button size="mini" @click="openGroup">选择人员</el-button>
            </el-form-item>
            <el-form-item label="负责人姓名" prop="directorName" required>
              <el-input size="mini" v-model.trim="ruleForm.directorName" disabled />
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button size="mini" @click="close()">取 消</el-button>
            <el-button size="mini" type="primary" @click="groupSave('ruleForm')">确 定</el-button>
          </span>
        </el-dialog>
      </basic-container>
    </el-col>
  </el-row>
</template>

<script>
import { mapGetters } from "vuex";
// 审核状态
import * as funList from "@/api/digitalGovernance/work.js";
import PersonSelect from "./personSelect.vue";
export default {
  components: { PersonSelect },
  data () {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      existList: [], // 编辑时存储回填数据
      option: {
        tip: false,
        searchShow: true,
        searchMenuSpan: 8,
        border: true,
        index: true,
        selection: true,
        dialogWidth: 600,
        dialogClickModal: false,
        labelWidth: 120,
        viewBtn: false,
        delBtn: false, // 默认是有删除、编辑按钮
        editBtn: false,
        addtBtn: false,
        column: [
          {
            label: "分组名称",
            prop: "name",
            searchSpan: 10,
            span: 24,
            search: true,
          },
          { label: "分组人数", prop: "num", addDisabled: false, editDisabled: false, filterable: false, viewDisplay: false },
          { label: "创建人", prop: "createUserName", addDisabled: false, editDisabled: false, filterable: false, viewDisplay: false },
          { label: "负责人姓名", type: 'textarea', prop: "userList", hide: true, span: 24, },
        ],
      },
      data: [],
      groupSaveVisible: false,
      groupVisible: false,
      ruleForm: {},
      rules: {
        name: [{ required: true, message: "请输入分组名称", trigger: "blur" }],
        director: [{ required: true, message: "请选择负责人" }],
        directorName: [{ required: true, message: "请选择负责人", trigger: "change" }]
      },
      director: '', // 负责人数据
    };
  },
  watch: {
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    // 优化后的权限计算属性
    permissionList () {
      return {
        addBtn: false,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
      };
    },
    ids () {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created () {
  },
  mounted () { },
  methods: {
    handleAdd (row, type) {
      if (type == 'edit') {
        funList.getGroupDet({ id: row.id }).then((res) => {
          const data = res.data.data
          this.ruleForm.userIds = data.userIds // 必用，注释报错userIds不存在
          this.ruleForm.director = data.userNames
          this.ruleForm.directorName = data.userNames
          this.ruleForm.name = data.name
          this.ruleForm.id = data.id
          const list = []
          for (let i = 0; i < data.userList.length; i++) {
            const element = data.userList[i];
            list[i] = {
              id: element.id,
              label: element.realName,
              leaf: true
            }
          }
          this.existList = list // 回显用
        }).finally(() => {
          this.groupSaveVisible = true
          this.$nextTick(() => {
            this.$refs.ruleForm.clearValidate()
          })
        });
      } else {
        this.existList = [] // 回显用
        this.groupSaveVisible = true
        this.$nextTick(() => {
          this.$refs.ruleForm.clearValidate()
        })
      }
    },
    groupSave (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.ruleForm.id) {
            // 编辑
            funList.getGroupUpdate(this.ruleForm).then(res => {
              this.$message({
                type: 'success',
                message: '操作成功!'
              })
              this.$refs[formName].resetFields()
              this.onLoad(this.page);
              this.$refs.personSelect.reset()
              this.close()
            })
          } else {
            funList.getGroupSave(this.ruleForm).then(res => {
              this.$message({
                type: 'success',
                message: '操作成功!'
              })
              this.$refs[formName].resetFields()
              this.onLoad(this.page);
              this.$refs.personSelect.reset()
              this.close()
            })
          }

        } else {
          return false
        }
      })
    },
    groupConfirm (data) {
      this.director = data.selectUsersName;
      this.ruleForm.userIds = data.selectUsers // 必用，注释报错userIds不存在
      this.ruleForm.director = data.selectUsers
      this.ruleForm.directorName = data.selectUsersName
      this.groupVisible = false
      this.$nextTick(() => {
        this.$refs.ruleForm.clearValidate(['directorName'])
      })
    },
    // 分组人员的取消
    close () {
      this.ruleForm = {}
      this.groupSaveVisible = false
    },
    groupClose () {
      this.groupVisible = false
    },
    openGroup() {
      this.groupVisible = true
      this.$nextTick(() => {
        this.$refs.personSelect.checkedNode = this.existList
      })
    },
    // 删除
    rowDel (row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return funList.getGroupDel({
            ids: row.id,
          });
        })
        .then(() => {
          this.onLoad(this.page, this.query);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    // 重置
    searchReset () {
      this.query = {};
      this.onLoad(this.page);
    },
    // 搜索
    searchChange (params, done) {
      this.page.currentPage = 1;
      this.query = params;
      this.onLoad(this.page, params);
      done();
    },
    // 勾选
    selectionChange (list) {
      this.selectionList = list;
    },
    // 重置勾选
    selectionClear () {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    // 多选删除
    handleDelete () {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return funList.getGroupDel({
            ids: this.ids,
          });
        })
        .then(() => {
          this.onLoad(this.page, this.query);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    // 查看预览
    // 打开前回调
    async beforeOpen (done, type) {
      if (type === "add") {
        this.form = {};
        done();
        return;
      }
      if (["edit", "view"].includes(type)) {
        funList.getGroupDet({ id: this.form.id })
          .then((res) => {
            this.form = res.data.data;
            const userList = []
            for (let i = 0; i < res.data.data.userList.length; i++) {
              const element = res.data.data.userList[i];
              userList.push(element.realName)
            }
            this.form.userList = userList.join(',')

          }).finally(() => {
            done();
          });
      }
    },
    // 当前页切换
    currentChange (currentPage) {
      this.page.currentPage = currentPage;
    },
    // 页面显示条数切换
    sizeChange (pageSize) {
      this.page.pageSize = pageSize;
    },
    // 刷新
    refreshChange () {
      this.onLoad(this.page, this.query);
    },
    // 首次加载
    async onLoad (page, params = {}) {
      this.loading = true;
      let query = {
        ...params,
        current: this.page.currentPage,
        size: this.page.pageSize,
      };

      let res = await funList.getGroupList(query);
      if (res && res.data && res.data.success) {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      }
    },
  },
};
</script>
<style scoped lang="scss">
.img-tiny {
  height: 100px;
  width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(245, 247, 250);
}

.btn-group-container {
  display: flex;
}
</style>
