<template>
  <div style="flex: 0 0 100%; position: relative; display: flex; flex-direction: column; padding: 1vh 1vw; box-sizing: border-box;" :class="{ 'rightP': isLeft, 'leftP': !isLeft }">
    <div style="flex: 0 0 100%; position: relative; display: flex; flex-direction: column;">
      <img class="img-common" src="/img/bigScreen/modulebg.png" mode="scaleToFill" />

      <HeaderView :idx="idx" :title="title"></HeaderView>


      <div ref="chart" style="flex: 1"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import HeaderView from '../common/headerView.vue';

export default {
  name: 'PreventView',
  components: {
    HeaderView,
  },
  props: {
    idx: {
      type: String,
      default: "05",
    },
    title: {
      type: String,
      default: "预警趋势",
    },
    isLeft: {
      type: Boolean,
      default: false,
    },
    hasMenu: {
      type: Boolean,
      default: true,
    },
    data() {
      return {
        myChart: null,
      };
    },
  },
  mounted() {
    this.initChart();
  },

  methods: {
    initChart() {
      this.myChart = echarts.init(this.$refs.chart);

      const option = {
          // backgroundColor: '#0f3460', // 深蓝色背景颜色
          tooltip: {
              trigger: 'axis'
          },
          legend: {
              data: ['防溺水', '山林防火'],
              textStyle: {
                  color: '#fff',
                  fontSize: '1.5vh',
              },
              icon: "rect",
              left: '5%', // 图例位置调整到右侧
              top: '5%' // 图例位置调整到顶部
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
              type: 'category',
              boundaryGap: true, // 设置为 true，使 X 轴的起始点与 Y 轴不相交
              data: ['','1月', '2月', '3月', '4月', '5月', '6月', ''],
              axisLabel: {
                  color: '#fff',
                  fontSize: 13,
                  fontWeight: 'lighter',
                  margin: 10,
                  offset: 10
              },
              axisTick: {
                alignWithLabel: true, // 刻度线与标签对齐
                show: false
              },
              axisLine: {
                onZero: false, // 使 X 轴线不与 Y 轴的零刻度对齐
                lineStyle: {
                  color: '#fff',
                  width: 0.5,
                }
              }
          },
          yAxis: {
              type: 'value',
              axisLabel: {
                  color: '#fff',
                  fontSize: 13,
                  fontWeight: 'lighter'
              },
              splitLine: {
                show: false,
                lineStyle: {
                  color: ['#336699'],
                  type: 'dashed'
                }
              },
              axisLine: {
                lineStyle: {
                  color: '#4c9aff'
                }
              },
              splitNumber: 4,
          },
          series: [
              {
                  name: '防溺水',
                  type: 'line',
                  smooth: true,
                  itemStyle: {
                      color: 'blue',
                      borderColor: '#fff', // 数据点边框颜色
                      borderWidth: 2, // 数据点边框宽度
                      shadowColor: 'rgba(0, 0, 0, 0.3)', // 数据点阴影颜色
                      shadowBlur: 10, // 数据点阴影模糊度
                      shadowOffsetY: 8 // 数据点阴影偏移量
                  },
                  lineStyle: {
                      width: 2,
                      shadowColor: 'rgba(0, 0, 0, 0.3)',
                      shadowBlur: 10,
                      shadowOffsetY: 8
                  },
                  areaStyle: {
                      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          { offset: 0, color: 'rgba(0, 128, 255, 0.8)' },
                          { offset: 1, color: 'rgba(0, 128, 255, 0.1)' }
                      ])
                  },
                  data: [1,5, 7, 9, 8, 12, 14,10]
              },
              {
                  name: '山林防火',
                  type: 'line',
                  smooth: true,
                  itemStyle: {
                      color: 'cyan',
                      borderColor: '#fff', // 数据点边框颜色
                      borderWidth: 2, // 数据点边框宽度
                      shadowColor: 'rgba(0, 0, 0, 0.3)', // 数据点阴影颜色
                      shadowBlur: 10, // 数据点阴影模糊度
                      shadowOffsetY: 8 // 数据点阴影偏移量
                  },
                  lineStyle: {
                      width: 2,
                      shadowColor: 'rgba(0, 0, 0, 0.3)',
                      shadowBlur: 10,
                      shadowOffsetY: 8
                  },
                  areaStyle: {
                      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          { offset: 0, color: 'rgba(0, 255, 255, 0.8)' },
                          { offset: 1, color: 'rgba(0, 255, 255, 0.1)' }
                      ])
                  },
                  data: [1,6, 8, 12, 10, 14, 16,4]
              }
          ]
      };

      this.myChart.setOption(option);
    }
  }

};
</script>

<style lang="scss" scoped>

@import "../common/common.scss";
.leftP {
  padding-left: 0.5vw !important;
}

.rightP {
  padding-right: 0.5vw !important;
}
</style>
