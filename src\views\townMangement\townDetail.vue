
<template>
  <div class="town-detail">
      <!-- 固定在顶部的操作按钮 -->
    <div class="operation-bar">
      <div v-if="!isEditing">
        <el-button type="primary" @click="startEdit">编辑</el-button>
      </div>
      <div v-else>
        <el-button type="primary" @click="saveEdit">保存</el-button>
        <el-button @click="cancelEdit">取消</el-button>
      </div>
    </div>
    <basic-container>
      <el-form :model="townForm" label-width="100px">
        <h3>镇级概况</h3>
        <el-form-item label="乡镇简介:">
          <el-input type="textarea" v-model="townForm.description" maxlength="300" :rows="5" show-word-limit style="width: 90%" size="small" :readonly="!isEditing"></el-input>
        </el-form-item>
        <el-form-item label="乡镇图片:">
          <el-upload action="#" limit="1" list-type="picture-card" :on-preview="handlePictureCardPreview" :on-remove="handleRemove" :disabled="!isEditing">
            <i class="el-icon-plus"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="镇视频:">
          <el-button size="small" type="primary">上传文件</el-button>
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="行政村数:">
              <el-input v-model="townForm.administrativeVillages" placeholder="请输入行政村数" :readonly="!isEditing"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总面积:">
              <el-input v-model="townForm.totalArea" placeholder="请输入总面积" :readonly="!isEditing"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="户籍数:">
              <el-input v-model="townForm.householdCount" placeholder="请输入户籍数" :readonly="!isEditing"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总人口:">
              <el-input v-model="townForm.totalPopulation" placeholder="请输入总人口" :readonly="!isEditing"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="学校:">
              <el-input v-model="townForm.schools" placeholder="请输入学校" :readonly="!isEditing"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="卫生站:">
              <el-input v-model="townForm.healthStations" placeholder="请输入卫生站" :readonly="!isEditing"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="居民广场:">
              <el-input v-model="townForm.residentSquares" placeholder="请输入居民广场" :readonly="!isEditing"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="便民服务中心:">
              <el-input v-model="townForm.citizenServiceCenter" placeholder="请输入便民服务中心" :readonly="!isEditing"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="table-header">
          <h3>领导班子</h3>
          <el-button type="primary" size="small" @click="addLeader(1)" v-if="isEditing">添加</el-button>
        </div>
        <el-table :data="leadershipList" style="width: 100%" :header-cell-style="headerCellStyle">
          <el-table-column prop="index" label="序号" width="80"></el-table-column>
          <el-table-column prop="name" label="姓名"></el-table-column>
          <el-table-column prop="position" label="职务"></el-table-column>
          <el-table-column prop="photo" label="照片"></el-table-column>
        </el-table>

        <div class="table-header">
          <h3>镇收入</h3>
          <el-button type="primary" size="small" @click="addLeader(2)" v-if="isEditing">添加</el-button>
        </div>
        <el-table :data="incomList" style="width: 100%" :header-cell-style="headerCellStyle">
          <el-table-column prop="index" label="序号" width="80"></el-table-column>
          <el-table-column prop="name" label="年份"></el-table-column>
          <el-table-column prop="position" label="镇集体年收入"></el-table-column>
          <el-table-column prop="photo" label="镇集体经营性年收入"></el-table-column>
        </el-table>

        <div class="table-header">
          <h3>一村一品</h3>
          <el-button type="primary" size="small" @click="addLeader(3)" v-if="isEditing">添加</el-button>
        </div>
        <el-table :data="productList" style="width: 100%" :header-cell-style="headerCellStyle">
          <el-table-column prop="index" label="序号" width="80"></el-table-column>
          <el-table-column prop="name" label="村镇"></el-table-column>
          <el-table-column prop="position" label="产品名称"></el-table-column>
          <el-table-column prop="photo" label="图片"></el-table-column>
        </el-table>

        <div class="table-header">
          <h3>特色产业</h3>
          <el-button type="primary" size="small" @click="addLeader(4)" v-if="isEditing">添加</el-button>
        </div>
        <el-table :data="industryList" style="width: 100%" :header-cell-style="headerCellStyle">
          <el-table-column prop="index" label="序号" width="80"></el-table-column>
          <el-table-column prop="name" label="产品名称"></el-table-column>
          <el-table-column prop="position" label="产品内容"></el-table-column>
          <el-table-column prop="photo" label="图片"></el-table-column>
        </el-table>
      </el-form>
    </basic-container>
    <el-dialog :visible.sync="dialogVisible" append-to-body="true" :close-on-click-modal="false" top="100px" width="30%"
      @close="close()">
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      townForm: {
        description: 'hahahahha',
        administrativeVillages: '',
        totalArea: '',
        householdCount: '',
        totalPopulation: '',
        schools: '',
        healthStations: '',
        residentSquares: '',
        citizenServiceCenter: ''
      },
      leadershipList: [
        { index: 1, name: '张三', position: '党委书记', photo: '-' }
      ],
      incomList: [],
      productList: [],
      industryList: [],
      isEditing: false,
      editType: 1,
      dialogVisible: false,
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    // permissionList() {
    //   return {
    //     addBtn: false,
    //     viewBtn: false,
    //     delBtn: false,
    //     editBtn: false
    //   };
    // },
    // ids() {
    //   let ids = [];
    //   this.selectionList.forEach(ele => {
    //     ids.push(ele.id);
    //   });
    //   return ids;
    // }
  },
  methods: {
    // 设置表头样式
    headerCellStyle({ row, column, rowIndex, columnIndex }) {
      return {
        background: '#dddddd', // 蓝色背景
        color: '#333',         // 白色文字
        fontWeight: 'bold'     // 加粗
      }
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
    },
    // 开始编辑
    startEdit() {
      this.isEditing = true
    },

    cancelEdit() {
      this.isEditing = false
    },

    saveEdit() {

    },
    addLeader(type) {
      this.editType = type
      this.dialogVisible = ture
      // 添加领导班子成员的逻辑
      this.$message.info('添加领导班子成员');
      // 示例：向 leadershipList 添加新项
      // const newIndex = this.leadershipList.length + 1;
      // this.leadershipList.push({
      //   index: newIndex,
      //   name: '',
      //   position: '',
      //   photo: ''
      // });
    }

  }
};
</script>

<style lang='scss' scoped>
.town-detail {
  // padding: 10px 6px;
  // box-sizing: border-box;
  // margin: 20px;
  // background: white;
  // border-radius: 8px;
  // padding: 40px;
  // width: 100%;
  // height: 100%;
  // overflow: scroll;
}

.operation-bar {
  position: sticky;
  left: 0;
  top: 0;
  background: white;
  padding: 10px 20px;
  z-index: 100;
  // margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 添加阴影效果，更明显看出 sticky 效果 */
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
