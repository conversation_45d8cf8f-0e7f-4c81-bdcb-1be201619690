<!--
 * @Description: 左上-领导班子
 * @Author: chenz76
 * @Date: 2021-10-26 14:42:02
 * @LastEditors: chenz76
 * @LastEditTime: 2023-06-06 09:52:16
-->
<template>
  <div style="flex: 0 0 100%; position: relative; display: flex; flex-direction: column; padding: 1vh 1vw; padding-right: 0.5vw; box-sizing: border-box;">
    <div style="flex: 0 0 100%; position: relative; display: flex; flex-direction: column;">
      <img class="img-common" src="/img/bigScreen/modulebg.png" mode="scaleToFill" />

      <HeaderView :idx="idx" :title="title"></HeaderView>
      <div style="display: flex; flex-direction: row; justify-content: space-between; padding: 0 1vw; margin-top: 2vh; margin-bottom: 1vh;">
        <div class="btn" @click="onTap(1)">
          <img class="img-common" :src="selectedType==1?'/img/bigScreen/btnSelected.png':'/img/bigScreen/btnNormal.png'" mode="scaleToFill" />
          <div class="head-idx" style="font-size: 1.2vh; font-weight: Medium;">镇村介绍</div>
        </div>
        <div class="btn" @click="onTap(2)">
          <img class="img-common" :src="selectedType==2?'/img/bigScreen/btnSelected.png':'/img/bigScreen/btnNormal.png'" mode="scaleToFill" />
          <div class="head-idx" style="font-size: 1.2vh; font-weight: Medium;">党支部介绍</div>
        </div>
        <div class="btn" @click="onTap(3)">
          <img class="img-common" :src="selectedType==3?'/img/bigScreen/btnSelected.png':'/img/bigScreen/btnNormal.png'" mode="scaleToFill" />
          <div class="head-idx" style="font-size: 1.2vh; font-weight: Medium;">领导班子</div>
        </div>
      </div>
      <div style="padding: 0 1vw;  margin-bottom: 0.74vh;">
        <div style="background: radial-gradient(36.32% 76.45% at -43.95% 50%, #499bff73 0%, #0073fe36 0%, #0073fe00 100%), radial-gradient(84.49% 76.45% at -43.95% 50%, #0073fe40 0%, #0073fe36 0%, #0073fe00 100%), linear-gradient(90deg, #0073fe40 -41.43%, #0073fe00 96.18%); border-radius: 4px; display: flex; flex-direction: row; ">
          <div style="position: relative; width: 8.8vw; height: 9.7vh;">

          </div>
          <div style="display: flex; flex-direction: column; justify-content: center;">
            <div class="head-title" style="font-size: 1.5vh;">锦尚镇</div>
            <div class="head-idx" style="font-size: 1.2vh;">福建省泉州市石狮市锦尚镇</div>
          </div>
        </div>
      </div>
      <div class="head-idx" style="font-size: 1.2vh; padding: 0 1vw; max-height: 6.8vh; text-overflow: ellipsis;">锦尚镇位于泉州湾口突出部，隶属泉州石狮市，于1999年建制，辖区总面积14.5平方公里，海岸线长8.4公里，辖锦尚、东店、厝上、杨厝、西港、深埕、谢厝、港东、卢厝、港前、奈厝前等11个行政村，户籍人口2.4万人，外来人口1.6万人（2024年数据），以纺织、染整、服装为主导产业。</div>
    </div>

  </div>
</template>
<script>
import HeaderView from '../common/headerView.vue';
export default {
  components: {
    HeaderView,
  },
  props: {
    townLeaderList: Array,
    villageLeaderList: Array,
    villageName:String,
    leftName:String,
    rightName:String,
    idx: {
      type: String,
      default: "01",
    },
    title: {
      type: String,
      default: "乡村介绍",
    },
  },
  data() {
    return {
      selectStatus: 0,
      showList: [],
      timer: null,
      flag:false,
      selectedType: 1,
    };
  },
  created() {
    // setTimeout(() => {
    // if(this.leftName!==''){
    //   this.changeSelectStatus(0);
    //   this.flag = false
    // }else if(this.rightName!==''){
    //   this.changeSelectStatus(1);
    //   this.flag = true
    // }
    // }, 1500);
    // setTimeout(() => {
      // if(this.leftName=="" && this.rightName==""){
      //   this.changeSelectStatus(0);
      //   this.flag = false
      // }else if( this.leftName==""){
      //   this.changeSelectStatus(1);
      //   this.flag = true
      // }else if(this.rightName==""){
      //   this.changeSelectStatus(0);
      //   this.flag = true
      // }else{
      //   this.changeSelectStatus(0);
      //   this.flag = false
      // }
    // }, 500);
    // this.startTimer();
  },
  // destroyed() {
  //   this.stopTimer();
  // },
  methods: {
    changeSelectStatus(status) {
      this.selectStatus = status;
      if (this.selectStatus === 0) {
        this.showList = this.townLeaderList;
      } else {
        this.showList = this.villageLeaderList;
      }
    },
    stopTimer() {
      // if (this.timer) clearInterval(this.timer);
    },
    // startTimer() {
      // this.timer = setInterval(() => {
      //   if (this.selectStatus === 0) {
      //     this.changeSelectStatus(1);
      //   } else {
      //     this.changeSelectStatus(0);
      //   }
      // }, 20 * 1000);
    // },
    onTap(type) {
      this.selectedType = type;
    }
  },
};
</script>

<style lang="scss" scoped>
.img-common {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

.head-idx {
  text-shadow: 0 4px 8px #00000040, 0 8px 16px #00000040;
  font-size: 1.7vh;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  background: linear-gradient(188deg, #C9F5FF 11.62%, #00FFFC 30.26%, #00A1FF 94.12%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
}

.head-title {
  text-shadow: 0 4px 8px #00000040, 0 8px 16px #00000040;
  font-size: 1.5vh;
  font-style: normal;
  font-weight: Bold;
  line-height: normal;
  background: linear-gradient(188deg, #C9F5FF 11.62%, #00FFFC 30.26%, #00A1FF 94.12%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: flex;
  align-items: center;
}

.btn {
  position: relative;
  width: 6vw;
  height: 2.2vh;
  cursor: pointer;
}
</style>
