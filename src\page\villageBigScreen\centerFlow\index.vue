<!--
 * @Description: 右下-民情统计、信箱统计
 * @Author: chenz76
 * @Date: 2021-10-26 14:42:02
 * @LastEditors: chenz76
 * @LastEditTime: 2022-01-06 10:38:58
-->
<template>
  <div style="flex: 0 0 100%; position: relative; display: flex; flex-direction: column; padding: 1vh 0; box-sizing: border-box;">
    <div style="flex: 0 0 100%; position: relative; display: flex; flex-direction: column; padding:2vh 1vw">
      <img class="img-common" src="/img/bigScreen/flowBg.png" mode="scaleToFill" />
      <div class="flow-title common-text">锦尚镇</div>
      <div class="data-grid">
        <div class="data-item" v-for="(item, index) in gridData" :key="index">
          <img class="img-common" src="/img/bigScreen/itemBg.png" mode="scaleToFill" />
          <div class="data-title" style="width: 100%; text-align: left; margin-left: 2vw;">{{ item.label }}</div>
          <div style="display: flex; justify-content: flex-end;">
            <div class="data-value">{{ item.value }}
            <span class="data-unit">{{ item.unit }}</span></div>
          </div>
        </div>
      </div>
      <div class="statistics-area" style="display: flex; flex-direction: row; margin-top: 0.74vh; padding: 0 1vw;">
        <!-- @mouseenter="handlePause1" @mouseleave="handleAutoLoop1" -->
        <div
            id="echartPie1"
            :style="{ width: '50%', height: '100%'}"
          ></div>
        <div
            id="echartPie2"
            :style="{ width: '50%', height: '100%'}"
          ></div>
      </div>
      <div class="foot-grid">
        <SparkGridItem v-for="(item, index) in footList" :key="index" :data="item"></SparkGridItem>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts';
import 'echarts-gl'
import { debounce } from "lodash";
import { getPublicMind, getMailbox } from "@/api/screen/screen";
import SparkGridItem from "../common/sparkGridItem.vue";
export default {

  components: {
    SparkGridItem,
  },
  data() {
    return {
      gridData: [
        {
          label: '户籍数',
          value: '1,000',
          valueKey: '',
          unit: '户',
        },
        {
          label: '户籍数',
          value: '1,000',
          valueKey: '',
          unit: '户',
        },
        {
          label: '户籍数',
          value: '1,000',
          valueKey: '',
          unit: '户',
        },
        {
          label: '户籍数',
          value: '1,000',
          valueKey: '',
          unit: '户',
        },
      ],
      footList: [
        {
          label: '随手拍',
          value: '720',
          valueKey: '',
          unit: '个',
        },
        {
          label: '随手拍',
          value: '720',
          valueKey: '',
          unit: '个',
        },
        {
          label: '随手拍',
          value: '720',
          valueKey: '',
          unit: '个',
        },
        {
          label: '随手拍',
          value: '720',
          valueKey: '',
          unit: '个',
        },
        {
          label: '随手拍',
          value: '720',
          valueKey: '',
          unit: '个',
        },
        {
          label: '随手拍',
          value: '720',
          valueKey: '',
          unit: '个',
        },

      ],
      alpha: 32,
      beta: 10,
      distance: 220,
      myChart1: null,
      myChart2: null,
      timer1: null,
      timer2: null,
      index1: 0,
      index2: 0,
      myChart1Data: [],
      myChart2Data: [],
      optionData: [
        { value: 70, name: '耕地', itemStyle: { color: "#0166F8" },},
        { value: 20, name: '林域', itemStyle: { color: "#FF80A2" }, },
        { value: 10, name: '水域', itemStyle: { color: "#F88501" }, },
      ],
      pieDataFlag: true,
    };
  },
  computed: {},
  mounted() {
    // this.getPublicMind();
    // this.getMailbox();
    // this.setResize();
    this.setPie(this.optionData, 'echartPie1', this.myChart1, '');
    this.setPie(this.optionData, 'echartPie2', this.myChart2, '');
  },
  created() {
    // setTimeout(() => {
    //   this.handleAutoLoop1();
    //   this.handleAutoLoop2();
    // }, 500);
  },
  destroyed() {
    // if (this.timer1) {
    //   clearInterval(this.timer1);
    // }
    // if (this.timer2) {
    //   clearInterval(this.timer2);
    // }
    // let self = this;
    // window.removeEventListener("resize", function () {
    //   if (self.myChart1) {
    //     self.myChart1 = null;
    //   }
    //   if (self.myChart2) {
    //     self.myChart2 = null;
    //   }
    // });
  },
  methods: {
    setPie(pieData, id, myChart, eventName) {
      if (myChart) myChart.dispose();
      var option = this.getPie3D(pieData, 0.6);
      //是否需要label指引线，如果要就添加一个透明的2d饼状图并调整角度使得labelLine和3d的饼状图对齐，并再次setOption
      // option.series.push({
      //   backgroundColor: "transparent",
      //   name: "pie2d",
      //   type: "pie",
      //   label: { opacity: 1, overflow: "none" },
      //   itemStyle: { opacity: 0.02 },
      //   labelLine: { length: 20, length2: 10 },
      //   startAngle: -60, // 起始角度，支持范围[0, 360]。
      //   clockwise: false, // 饼图的扇区是否是顺时针排布。上述这两项配置主要是为了对齐3d的样式
      //   radius: ["30%", "55%"],
      //   center: ["50%", "50%"],
      //   data: pieData, // 与之前的series数据一致
      //   tooltip: {
      //     show: false,
      //   },
      // });

      myChart = echarts.init(document.getElementById(id));
      myChart.setOption(option);
      // myChart.off("click"); // 防止重复绑定点击事件
      // 添加点击事件
      // myChart.on("click", (params) => eventName(params, this.mapTitle));
      // window.addEventListener("resize", () => myChart.resize());
    },

    //自适应
    // setResize() {
    //   let self = this;
    //   window.addEventListener(
    //     "resize",
    //     debounce(function () {
    //       if (self.myChart1) {
    //         self.myChart1.resize();
    //       }
    //       if (self.myChart2) {
    //         self.myChart2.resize();
    //       }
    //     }, 200)
    //   );
    // },

    //开始自动轮播函数
    handleAutoLoop1() {
      if (this.timer1) {
        clearInterval(this.timer1);
      }
      this.timer1 = setInterval(() => {
        if (this.index1 > 0) {
          this.myChart1.dispatchAction({
            type: "downplay",
            seriesIndex: 0,
            dataIndex: this.index1 - 1,
          });
        }
        this.myChart1.dispatchAction({
          type: "highlight",
          seriesIndex: 0,
          dataIndex: this.index1,
        });
        this.index1 += 1;
        if (this.index1 >= this.myChart1Data.length) {
          this.index1 = 0;
          setTimeout(() => {
            this.myChart1.dispatchAction({
              type: "downplay",
              seriesIndex: 0,
              dataIndex: this.myChart1Data.length - 1,
            });
          }, 5000);
        }
      }, 5000);
    },
    //停止自动轮播函数
    handlePause1() {
      clearInterval(this.timer1);
      this.myChart1.dispatchAction({
        type: "downplay",
        seriesIndex: 0,
        dataIndex: this.index1 - 1,
      });
    },

    //开始自动轮播函数
    handleAutoLoop2() {
      if (this.timer2) {
        clearInterval(this.timer2);
      }
      this.timer2 = setInterval(() => {
        if (this.index2 > 0) {
          this.myChart2.dispatchAction({
            type: "downplay",
            seriesIndex: 0,
            dataIndex: this.index2 - 1,
          });
        }
        this.myChart2.dispatchAction({
          type: "highlight",
          seriesIndex: 0,
          dataIndex: this.index2,
        });
        this.index2 += 1;
        if (this.index2 >= this.myChart2Data.length) {
          this.index2 = 0;
          setTimeout(() => {
            this.myChart2.dispatchAction({
              type: "downplay",
              seriesIndex: 0,
              dataIndex: this.myChart2Data.length - 1,
            });
          }, 5000);
        }
      }, 5000);
    },
    //停止自动轮播函数
    handlePause2() {
      clearInterval(this.timer2);
      this.myChart2.dispatchAction({
        type: "downplay",
        seriesIndex: 0,
        dataIndex: this.index2 - 1,
      });
    },
    onTap(type) {
      this.selectedType = type;
    },
    getPie3D(pieData, internalDiameterRatio) {
      // internalDiameterRatio:透明的空心占比
      let series = [];
      let sumValue = 0;
      let startValue = 0;
      let endValue = 0;
      let k =
          typeof internalDiameterRatio !== 'undefined'
            ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
            : 1 / 3
      pieData.sort((a, b) => {
        return b.value - a.value;
      });
      // 为每一个饼图数据，生成一个 series-surface 配置
      for (let i = 0; i < pieData.length; i++) {
        sumValue += pieData[i].value;
        let seriesItem = {
          name: typeof pieData[i].name === "undefined" ? `series${i}` : pieData[i].name,
          type: "surface",
          parametric: true,
          wireframe: {
            show: false,
          },
          pieData: pieData[i],
          pieStatus: {
            selected: false,
            hovered: false,
            k: k,
          },
          radius: "50%",
          center: ["10%", "10%"],
        };

        if (typeof pieData[i].itemStyle != "undefined") {
          let itemStyle = {};
          typeof pieData[i].itemStyle.color != "undefined" ? (itemStyle.color = pieData[i].itemStyle.color) : null;
          typeof pieData[i].itemStyle.opacity != "undefined"
            ? (itemStyle.opacity = pieData[i].itemStyle.opacity)
            : null;
          seriesItem.itemStyle = itemStyle;
        }
        series.push(seriesItem);
      }

      // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
      // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。

      for (let i = 0; i < series.length; i++) {
        endValue = startValue + series[i].pieData.value;
        series[i].pieData.startRatio = startValue / sumValue;
        series[i].pieData.endRatio = endValue / sumValue;
        series[i].parametricEquation = this.getParametricEquation(
          series[i].pieData.startRatio,
          series[i].pieData.endRatio,
          false,
          false,
          k,
          series[i].pieData.value
        );
        startValue = endValue;
      }
      let boxHeight = this.getHeight3D(series, 30); //通过传参设定3d饼/环的高度，26代表26px
      // 准备待返回的配置项，把准备好的 legendData、series 传入。
      let option = {
        legend: {
          orient: "vertical",
          show: true,
          right: 0,
          top: "auto",
          textStyle: {
            color: '#fff'
          },
        },
        // 引导线配置
        labelLine: {
          show: true,
          lineStyle: {
            color: "#fff",
            normal: {},
          },
        },
        label: {
          show: true,
          position: "outside",
          // formatter: function (params) {
          //   return `{d|${params.percent?.toFixed(2)}}{c|${"%"}}\n\n{b|${params.name}}`;
          // },
          // rich: {
          //   d: {
          //     fontSize: 22,
          //     lineHeight: 18,
          //     fontFamily: "Source Han Sans",
          //     fontWeight: "bold",
          //     color: "#A7E0FF",
          //   },
          //   b: {
          //     fontSize: 18,
          //     color: "#fff",
          //   },
          //   c: {
          //     fontSize: 16,
          //     lineHeight: 18,
          //     color: "#fff",
          //   },
          // },
        },
        // 提示框
        tooltip: {
          // position: "right", // 将提示框显示在鼠标右侧
          textStyle: { color: "#fff", fontSize: 14 },
          borderWidth: 1,
          borderColor: "#1495F4",
          backgroundColor: "rgba(20, 149, 244, 0.5)", // 提示框背景颜色
          boxShadow: "0 0 3px rgba(0, 0, 0, 0.3)", // 提示框阴影
          extraCssText: "box-shadow: inset 0px 0px 10px 0px rgba(1, 30, 18, 0.302);",
          // 格式化提示框，添加单位
          formatter: (params) => {
            if (params.seriesName !== "mouseoutSeries" && params.seriesName !== "pie2d") {
              let bfb = 0;
              if (this.pieDataFlag) {
                let findItem = pieData.find((item) => item.name === params.seriesName);
                bfb =  findItem.value;
              }
              const unit = ["万个", "万户"];
              const format = unit.includes(this.unit) ? 2 : 0;
              return (
                "<div style='color: #fff''>" +
                `${params.seriesName}<br/>` +
                `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params.color};"></span>` +
                `${bfb}%` +
                "</div>"
              );
            }
          },
        },
        xAxis3D: {
          min: -1,
          max: 1,
        },
        yAxis3D: {
          min: -1,
          max: 1,
        },
        zAxis3D: {
          min: -1,
          max: 1,
        },
        grid3D: {
          show: false,
          boxHeight: boxHeight, //圆环的高度
          left: 0,
          top: 0, //3d饼图的位置
          viewControl: {
            //3d效果可以放大、旋转等，请自己去查看官方配置
            alpha: this.alpha, //角度
            // 饼块开始得角度
            beta: this.beta || 60,
            distance: this.distance, //调整视角到主体的距离，类似调整zoom
            rotateSensitivity: 0, //设置为0无法旋转
            zoomSensitivity: 0, //设置为0无法缩放
            panSensitivity: 0, //设置为0无法平移
            // autoRotate: true //自动旋转
          },
        },
        series: series,
      };
      return option;
    },
    //获取3d丙图的最高扇区的高度
    getHeight3D(series, height) {
      series.sort((a, b) => {
        return b.pieData.value - a.pieData.value;
      });
      return (height * 25) / series[0].pieData.value;
    },
    // 生成扇形的曲面参数方程，用于 series-surface.parametricEquation
    getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {
      // 计算
      let midRatio = (startRatio + endRatio) / 2;
      let startRadian = startRatio * Math.PI * 2;
      let endRadian = endRatio * Math.PI * 2;
      let midRadian = midRatio * Math.PI * 2;
      // 如果只有一个扇形，则不实现选中效果。
      if (startRatio === 0 && endRatio === 1) {
        isSelected = true;
      }
      // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
      k = typeof k !== "undefined" ? k : 1 / 3;
      // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
      let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
      let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;
      // 计算高亮效果的放大比例（未高亮，则比例为 1）
      let hoverRate = isHovered ? 1.05 : 1;
      // 返回曲面参数方程
      return {
        u: {
          min: -Math.PI,
          max: Math.PI * 3,
          step: Math.PI / 32,
        },
        v: {
          min: 0,
          max: Math.PI * 2,
          step: Math.PI / 20,
        },
        x: function (u, v) {
          if (u < startRadian) {
            return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
          }
          if (u > endRadian) {
            return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
          }
          return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
        },
        y: function (u, v) {
          if (u < startRadian) {
            return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
          }
          if (u > endRadian) {
            return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
          }
          return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
        },
        z: function (u, v) {
          if (u < -Math.PI * 0.5) {
            return Math.sin(u);
          }
          if (u > Math.PI * 2.5) {
            return Math.sin(u) * h * 0.1;
          }
          return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;
        },
      };
    },
  },
};
</script>

<style lang="scss" scoped>

.img-common {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

.flow-title {
  font-size: 1.86vh;
  font-style: normal;
  font-weight: Bold;
  line-height: normal;
  background: linear-gradient(188deg, #C9F5FF 11.62%, #00FFFC 30.26%, #00A1FF 94.12%);
  margin-top: 2.2vh;
  margin-left: 0.78vw;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.data-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr); // 4列
  grid-template-rows: repeat(1, 1fr);    // 2行
  gap: 0 3px;                          // 行间距和列间距
  padding: 0 0.6vw;
  margin-top: 0.7vh;
}

.data-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  // width: 5.4vw;
  height: 7.4vh;
  position: relative;
}

.data-title {
  color: #ffffff;
  text-align: center;
  font-size: 1.1vh;
  font-weight: normal;
}

.data-value {
  font-size: 2.6vh;
  font-style: normal;
  font-weight: bold;
  line-height: normal;
  background: linear-gradient(188deg, #C9F5FF 11.62%, #00FFFC 30.26%, #00A1FF 94.12%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.data-unit {
  text-shadow: 0 4px 8px #00000040, 0 8px 16px #00000040;
  font-size: 0.74vh;
  font-style: normal;
  font-weight: bold;
  line-height: normal;
  background: linear-gradient(188deg, #C9F5FF 11.62%, #00FFFC 30.26%, #00A1FF 94.12%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.foot-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr); // 4列
  grid-template-rows: repeat(1, 1fr);    // 2行
  gap: 0;                          // 行间距和列间距
  padding: 2vh 1.14vw;
}
</style>
