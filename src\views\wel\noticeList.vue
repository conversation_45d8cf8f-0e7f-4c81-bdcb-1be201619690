<template>
  <basic-container>
    <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" v-model="form" ref="crud" @row-click="handleRowClick" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
    </avue-crud>

    <!-- 通知详情弹窗 -->
    <el-dialog
      title="通知详情"
      :visible.sync="dialogVisible"
      width="60%"
      :close-on-click-modal="false"
      append-to-body
      @close="handleDialogClose"
      class="notice-detail-dialog"
    >
      <div class="notice-detail-content" v-if="showData && Object.keys(showData).length > 0">
        <!-- 标题 -->
        <div class="detail-item">
          <label class="detail-label">标题：</label>
          <div class="detail-value title-value">{{ showData.title }}</div>
        </div>

        <!-- 消息类型 -->
        <div class="detail-item">
          <label class="detail-label">消息类型：</label>
          <div class="detail-value">{{ showData.noticeTypeText || '未知类型' }}</div>
        </div>

        <!-- 发布时间 -->
        <div class="detail-item">
          <label class="detail-label">发布时间：</label>
          <div class="detail-value">{{ showData.releaseTime }}</div>
        </div>

        <!-- 内容 -->
        <div class="detail-item content-item">
          <label class="detail-label">内容：</label>
          <div class="detail-value content-value" v-html="sanitizeHtml(showData.content)"></div>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleDialogClose">关闭</el-button>
      </span>
    </el-dialog>
  </basic-container>
</template>

<script>
import { getUserList,userRead } from "@/api/desk/notice";

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      dialogVisible: false, // 控制弹窗显示
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: false,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        menu: false,
        addBtn: false,
        selection: false,
        dialogClickModal: false,
        column: [
          {
            label: "标题",
            prop: "title",
            searchSpan: 6,
            span: 24,
            search: false,
            maxlength: 100,
            showWordLimit: true,
            rules: [
              {
                required: true,
                whitespace: true,
                message: "请输入标题",
                trigger: ["blur", "change"],
              },
            ],
          },

          {
            label: "消息类型",
            prop: "noticeType",
            type: "select",
            dicUrl: "/api/blade-system/dict/dictionary?code=noticeType",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            // dataType: "number",
            // slot: true,
            search: true,
          },
          {
            label: "发布时间",
            prop: "releaseTime",
            slot: true,
            addDisplay: false, // 表单新增时是否可见
            editDisplay: false,
            viewDisplay: false,
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
        ],
      },
      data: [],
      showData:{},
    };
  },
  computed: {
  },
  methods: {

    handleRowClick(row) {
      this.showData = row;
      this.dialogVisible = true;
    },

    // 关闭弹窗并清空数据
    handleDialogClose() {
      this.dialogVisible = false;
      this.showData = {};
    },

    // HTML 内容安全处理，防止 XSS 攻击
    sanitizeHtml(html) {
      if (!html) return '';

      // 基本的 HTML 标签白名单
      const allowedTags = ['p', 'br', 'strong', 'b', 'em', 'i', 'u', 'span', 'div', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'ul', 'ol', 'li', 'blockquote'];
      const allowedAttributes = ['style', 'class'];

      // 移除危险的标签和属性
      let sanitized = html
        .replace(/<script[^>]*>.*?<\/script>/gi, '') // 移除 script 标签
        .replace(/<iframe[^>]*>.*?<\/iframe>/gi, '') // 移除 iframe 标签
        .replace(/javascript:/gi, '') // 移除 javascript: 协议
        .replace(/on\w+\s*=/gi, '') // 移除事件处理器
        .replace(/<object[^>]*>.*?<\/object>/gi, '') // 移除 object 标签
        .replace(/<embed[^>]*>/gi, '') // 移除 embed 标签
        .replace(/<form[^>]*>.*?<\/form>/gi, ''); // 移除 form 标签

      return sanitized;
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getUserList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.notice-detail-dialog {
  .notice-detail-content {
    .detail-item {
      margin-bottom: 20px;
      display: flex;
      align-items: flex-start;

      .detail-label {
        font-weight: bold;
        color: #606266;
        min-width: 80px;
        margin-right: 10px;
        flex-shrink: 0;
        line-height: 1.5;
      }

      .detail-value {
        flex: 1;
        color: #303133;
        line-height: 1.5;
        word-break: break-word;

        &.title-value {
          font-size: 16px;
          font-weight: 500;
          color: #409EFF;
        }

        &.content-value {
          border: 1px solid #DCDFE6;
          border-radius: 4px;
          padding: 12px;
          background-color: #FAFAFA;
          min-height: 100px;
          max-height: 300px;
          overflow-y: auto;

          // 内容区域的 HTML 样式
          ::v-deep {
            p { margin: 8px 0; }
            h1, h2, h3, h4, h5, h6 { margin: 12px 0 8px 0; }
            ul, ol { margin: 8px 0; padding-left: 20px; }
            li { margin: 4px 0; }
            blockquote {
              margin: 8px 0;
              padding: 8px 12px;
              border-left: 4px solid #409EFF;
              background-color: #F0F9FF;
            }
            strong, b { font-weight: bold; }
            em, i { font-style: italic; }
            u { text-decoration: underline; }
          }
        }
      }

      &.content-item {
        align-items: flex-start;

        .detail-label {
          margin-top: 12px;
        }
      }
    }
  }
}

// 响应式设计
@media screen and (max-width: 768px) {
  .notice-detail-dialog {
    ::v-deep .el-dialog {
      width: 95% !important;
      margin: 20px auto !important;
    }

    .notice-detail-content {
      .detail-item {
        flex-direction: column;

        .detail-label {
          margin-bottom: 8px;
          margin-right: 0;
        }

        &.content-item .detail-label {
          margin-top: 0;
        }
      }
    }
  }
}
</style>
