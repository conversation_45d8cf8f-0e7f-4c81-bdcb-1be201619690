<template>
  <basic-container>
    <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" :row-class-name="tableRowClassName" v-model="form" ref="crud" @row-click="handleRowClick" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
    </avue-crud>

    <!-- 通知详情弹窗 -->
    <el-dialog
      :visible.sync="dialogVisible"
      width="70%"
      :close-on-click-modal="false"
      append-to-body
      @close="handleDialogClose"
      class="notice-detail-dialog"
      :show-close="false"
      top="5vh"
    >
      <!-- 自定义标题栏 -->
      <div slot="title" class="dialog-header">
        <div class="header-content">
          <i class="el-icon-bell header-icon"></i>
          <span class="header-title">通知详情</span>
        </div>
        <div class="header-actions">
          <el-button
            type="text"
            icon="el-icon-close"
            @click="handleDialogClose"
            class="close-btn"
          ></el-button>
        </div>
      </div>

      <div class="notice-detail-content" v-if="showData && Object.keys(showData).length > 0">
        <!-- 通知卡片 -->
        <div class="notice-card">
          <!-- 标题区域 -->
          <div class="title-section">
            <div class="title-wrapper">
              <h2 class="notice-title">{{ showData.title }}</h2>
              <div class="notice-meta">
                <el-tag
                  :type="showData.isRead ? 'info' : 'primary'"
                  size="mini"
                  class="read-status"
                >
                  {{ showData.isRead ? '已读' : '未读' }}
                </el-tag>
                <span class="notice-type">{{ showData.noticeTypeText || '未知类型' }}</span>
              </div>
            </div>
          </div>

          <!-- 时间信息 -->
          <div class="time-section">
            <i class="el-icon-time time-icon"></i>
            <span class="time-text">发布时间：{{ showData.releaseTime }}</span>
          </div>

          <!-- 分割线 -->
          <el-divider class="content-divider"></el-divider>

          <!-- 内容区域 -->
          <div class="content-section">
            <div class="content-header">
              <i class="el-icon-document content-icon"></i>
              <span class="content-label">通知内容</span>
            </div>
            <div class="content-body" v-html="sanitizeHtml(showData.content)"></div>
          </div>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleDialogClose" size="medium">
          <i class="el-icon-check"></i>
          我知道了
        </el-button>
      </span>
    </el-dialog>
  </basic-container>
</template>

<script>
import { getUserList,userRead } from "@/api/desk/notice";

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      dialogVisible: false, // 控制弹窗显示
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: false,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        menu: false,
        addBtn: false,
        selection: false,
        dialogClickModal: false,
        column: [
          {
            label: "标题",
            prop: "title",
            searchSpan: 6,
            span: 24,
            search: false,
            maxlength: 100,
            showWordLimit: true,
            rules: [
              {
                required: true,
                whitespace: true,
                message: "请输入标题",
                trigger: ["blur", "change"],
              },
            ],
          },

          {
            label: "消息类型",
            prop: "noticeType",
            type: "select",
            dicUrl: "/api/blade-system/dict/dictionary?code=noticeType",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            // dataType: "number",
            // slot: true,
            search: true,
          },
          {
            label: "发布时间",
            prop: "releaseTime",
            slot: true,
            addDisplay: false, // 表单新增时是否可见
            editDisplay: false,
            viewDisplay: false,
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
        ],
      },
      data: [],
      showData:{},
    };
  },
  computed: {
  },
  methods: {

     tableRowClassName ({ row }) {
        if (row.isRead ) {
          return 'hasRead';
        } else {
          return 'notRead';
        }
          
        
     },
    handleRowClick(row) {
      this.showData = row;
      this.dialogVisible = true;
    },

    // 关闭弹窗并清空数据
    handleDialogClose() {
      // 如果消息未读，则调用已读接口
      if (this.showData && this.showData.id && !this.showData.isRead ) {
        userRead(this.showData.id).then(() => {
          // 标记为已读成功后，更新本地数据
          const index = this.data.findIndex(item => item.id === this.showData.id);
          if (index !== -1) {
            this.$set(this.data[index], 'isRead', true);
            this.$refs.crud.refreshTable()
          }
        }).catch(error => {
          console.error('标记已读失败:', error);
        });
      }

      this.dialogVisible = false;
      this.showData = {};
    },

    // HTML 内容安全处理，防止 XSS 攻击
    sanitizeHtml(html) {
      if (!html) return '';

      // 移除危险的标签和属性
      let sanitized = html
        .replace(/<script[^>]*>.*?<\/script>/gi, '') // 移除 script 标签
        .replace(/<iframe[^>]*>.*?<\/iframe>/gi, '') // 移除 iframe 标签
        .replace(/javascript:/gi, '') // 移除 javascript: 协议
        .replace(/on\w+\s*=/gi, '') // 移除事件处理器
        .replace(/<object[^>]*>.*?<\/object>/gi, '') // 移除 object 标签
        .replace(/<embed[^>]*>/gi, '') // 移除 embed 标签
        .replace(/<form[^>]*>.*?<\/form>/gi, ''); // 移除 form 标签

      return sanitized;
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getUserList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.notice-detail-dialog {
  ::v-deep .el-dialog {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  ::v-deep .el-dialog__header {
    padding: 0;
    border-bottom: 1px solid #f0f0f0;
  }

  ::v-deep .el-dialog__body {
    padding: 0;
  }

  ::v-deep .el-dialog__footer {
    padding: 20px 30px;
    background-color: #fafbfc;
    border-top: 1px solid #f0f0f0;
    text-align: center;
  }

  // 自定义标题栏
  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    .header-content {
      display: flex;
      align-items: center;

      .header-icon {
        font-size: 20px;
        margin-right: 10px;
        color: #fff;
      }

      .header-title {
        font-size: 18px;
        font-weight: 600;
        color: #fff;
      }
    }

    .header-actions {
      .close-btn {
        color: #fff;
        font-size: 18px;
        padding: 8px;

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
        }
      }
    }
  }

  // 通知内容区域
  .notice-detail-content {
    padding: 30px;

    .notice-card {
      background: #fff;

      // 标题区域
      .title-section {
        margin-bottom: 20px;

        .title-wrapper {
          .notice-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            margin: 0 0 12px 0;
            line-height: 1.4;
          }

          .notice-meta {
            display: flex;
            align-items: center;
            gap: 12px;

            .read-status {
              font-weight: 500;
            }

            .notice-type {
              color: #7f8c8d;
              font-size: 14px;
              padding: 4px 12px;
              background-color: #f8f9fa;
              border-radius: 12px;
              border: 1px solid #e9ecef;
            }
          }
        }
      }

      // 时间区域
      .time-section {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        color: #7f8c8d;
        font-size: 14px;

        .time-icon {
          margin-right: 8px;
          color: #95a5a6;
        }

        .time-text {
          font-weight: 500;
        }
      }

      // 分割线
      .content-divider {
        margin: 20px 0;
        border-color: #e9ecef;
      }

      // 内容区域
      .content-section {
        .content-header {
          display: flex;
          align-items: center;
          margin-bottom: 16px;

          .content-icon {
            color: #3498db;
            margin-right: 8px;
            font-size: 16px;
          }

          .content-label {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
          }
        }

        .content-body {
          background-color: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 20px;
          min-height: 120px;
          max-height: 400px;
          overflow-y: auto;
          line-height: 1.6;
          color: #2c3e50;

          // 内容区域的 HTML 样式
          ::v-deep {
            p {
              margin: 12px 0;
              &:first-child { margin-top: 0; }
              &:last-child { margin-bottom: 0; }
            }
            h1, h2, h3, h4, h5, h6 {
              margin: 20px 0 12px 0;
              color: #2c3e50;
              font-weight: 600;
            }
            ul, ol {
              margin: 12px 0;
              padding-left: 24px;
            }
            li {
              margin: 6px 0;
              line-height: 1.6;
            }
            blockquote {
              margin: 16px 0;
              padding: 12px 16px;
              border-left: 4px solid #3498db;
              background-color: #ebf3fd;
              border-radius: 0 4px 4px 0;
            }
            strong, b {
              font-weight: 600;
              color: #2c3e50;
            }
            em, i {
              font-style: italic;
              color: #7f8c8d;
            }
            u {
              text-decoration: underline;
            }
            img {
              max-width: 100%;
              height: auto;
              border-radius: 4px;
              margin: 8px 0;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media screen and (max-width: 768px) {
  .notice-detail-dialog {
    ::v-deep .el-dialog {
      width: 95% !important;
      margin: 10px auto !important;
      top: 5vh !important;
    }

    .dialog-header {
      padding: 16px 20px !important;

      .header-content .header-title {
        font-size: 16px !important;
      }
    }

    .notice-detail-content {
      padding: 20px !important;

      .notice-card {
        .title-section .title-wrapper .notice-title {
          font-size: 20px !important;
        }

        .notice-meta {
          flex-direction: column;
          align-items: flex-start !important;
          gap: 8px !important;
        }

        .content-section .content-body {
          padding: 16px !important;
          max-height: 300px !important;
        }
      }
    }

    ::v-deep .el-dialog__footer {
      padding: 16px 20px !important;
    }
  }
}

@media screen and (max-width: 480px) {
  .notice-detail-dialog {
    ::v-deep .el-dialog {
      width: 98% !important;
      margin: 5px auto !important;
    }

    .notice-detail-content {
      padding: 16px !important;

      .notice-card {
        .title-section .title-wrapper .notice-title {
          font-size: 18px !important;
        }

        .content-section .content-body {
          padding: 12px !important;
          max-height: 250px !important;
        }
      }
    }
  }
}

// 表格行样式 - 已读和未读状态
::v-deep .el-table {
  .hasRead {
    background-color: #f5f7fa !important;
    color: #909399;

    &:hover {
      background-color: #ebeef5 !important;
    }

    td {
      color: #909399;
    }
  }

  .notRead {
    background-color: #fff !important;
    color: #303133;
    font-weight: 500;

    &:hover {
      background-color: #f5f7fa !important;
    }

    td {
      color: #303133;
    }

    td:first-child {
      position: relative;
    }
  }
}
</style>
