<template>
  <basic-container>
    <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" v-model="form" ref="crud" @row-click="handleRowClick" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
    </avue-crud>
  </basic-container>
</template>

<script>
import { getUserList } from "@/api/desk/notice";

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: false,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        menu: false,
        addBtn: false,
        selection: false,
        dialogClickModal: false,
        column: [
          {
            label: "标题",
            prop: "title",
            searchSpan: 6,
            span: 24,
            search: false,
            maxlength: 100,
            showWordLimit: true,
            rules: [
              {
                required: true,
                whitespace: true,
                message: "请输入标题",
                trigger: ["blur", "change"],
              },
            ],
          },

          {
            label: "消息类型",
            prop: "noticeType",
            type: "select",
            dicUrl: "/api/blade-system/dict/dictionary?code=noticeType",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            // dataType: "number",
            // slot: true,
            search: true,
          },
          {
            label: "发布时间",
            prop: "releaseTime",
            slot: true,
            addDisplay: false, // 表单新增时是否可见
            editDisplay: false,
            viewDisplay: false,
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
        ],
      },
      data: [],
      showData:{},
    };
  },
  computed: {
  },
  methods: {

    handleRowClick(row, event, column) {
      this.showData = row;
      // this.$notify({
      //   showClose: true,
      //   message: '单击' + JSON.stringify(row),
      //   type: 'success',
      // });
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getUserList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    }
  }
};
</script>

<style>
</style>
