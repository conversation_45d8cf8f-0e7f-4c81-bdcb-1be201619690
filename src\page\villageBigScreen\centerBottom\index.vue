<!--
 * @Description: 中下-三务公开
 * @Author: chenz76
 * @Date: 2021-10-26 14:42:02
 * @LastEditors: chenz76
 * @LastEditTime: 2024-02-01 15:14:32
-->
<template>
  <div class="center-bottom-content1">
    <div class="center-bottom-header">
      <img src="/img/screen/title-decorate-center.png" class="left-decorate" alt="" />
      <span class="title">三务公开</span>
      <img src="/img/screen/title-decorate-center.png" class="right-decorate" alt="" />
    </div>
    <div class="content" @mouseenter="stopTimer" @mouseleave="startTimer">
      <img src="/img/screen/title-select-back.png" :style="
        selectStatus === 0
          ? 'left:0'
          : selectStatus === 1
            ? 'left:33%'
            : 'right:0'
      " class="title-select-back" alt="" />
      <div class="select-1" @click="changeSelectStatus(0)">党务公开</div>
      <div class="select-2" @click="changeSelectStatus(1)">村务公开</div>
      <div class="select-3" @click="changeSelectStatus(2)">财务公开</div>
      <div class="content-value" v-if="isShow" style="height:15vh;">
        <dv-scroll-board :config="this.config" ref="scrollBoard" />
      </div>
      <div class="content-value" v-else style="height:15vh;">
        <dv-scroll-board :config="this.config" ref="scrollBoard" />
      </div>
    </div>
  </div>
</template>
<script>
import "./index.scss";
import { getThreeAffairsAll } from "@/api/screen/screen";
import { ScrollBoard } from '@jiaminghi/data-view'

export default {
  components: {
    'dv-scroll-board': ScrollBoard
  },
  props: {
    isShow: {
      type:Boolean,
      default:false
    },
    deptId:{
      type:String,
      default:''
    }
  },
  data() {
    return {
      selectStatus: 0,
      config: {
        header: [
          "<span>标题</span>",
          // "<span>关键字</span>",
          "<span>发布日期</span>",
        ],
        rowNum: 3,
        headerBGC: "",
        headerHeight: 20,
        columnWidth: [540, 380],
        evenRowBGC: "#102359",
        oddRowBGC: "",
        data: [],
      },
      timer: null,

      threeAffairsAllData: [],
    };
  },
  created() {
    //获取三务公开
    // this.getThreeAffairsAll();
    // this.startTimer();
  },
  destroyed() {
    this.stopTimer();
  },
  methods: {
    async getThreeAffairsAll() {
      let params = {
        current: 1,
        size: 10,
        deptId:this.deptId
      };
      const result = await new Promise((resolve) => {
        getThreeAffairsAll(params).then((res) => {
          if (res && res.data.code === 200) {
            resolve(res.data.data);
          }
        });
      });
      this.threeAffairsAllData = result;
      this.changeSelectStatus(0);
    },
    async getThreeAffairs(renderData) {
      let data = renderData.records;
      let countfinancialList = [];
      for (let i = 0; i < data.length; i++) {
        let editData = [];
        editData[0] =
          "<span style='color:#fff' title='" +
          data[i].title +
          "'>" +
          data[i].title +
          "</span>";
        // editData[1] =
        //   "<span style='color:#fff' title='" +
        //   data[i].keyword +
        //   "'>" +
        //   data[i].keyword +
        //   "</span>";
        editData[1] =
          "<span style='color:#fff' title='" +
          data[i].updateTime +
          "'>" +
          data[i].updateTime +
          "</span>";
        countfinancialList[i] = editData;
      }
      this.config = {
        header: [
          "<span>标题</span>",
          //"<span>关键字</span>",
          "<span>发布日期</span>",
        ],
        rowNum: 3,
        headerBGC: "",
        headerHeight: 30,
        columnWidth: [540, 380],
        evenRowBGC: "#102359",
        oddRowBGC: "",
        data: countfinancialList,
      };
    },
    changeSelectStatus(status) {
      this.selectStatus = status;
      if (this.threeAffairsAllData.length > 2) {
        /**
         * selectStatus
         * 0 : 党务公开
         * 1 : 财务公开
         * 2 : 村务公开
         */
        this.getThreeAffairs(this.threeAffairsAllData[this.selectStatus]);
      }
    },
    stopTimer() {
      if (this.timer) clearInterval(this.timer);
    },
    startTimer() {
      this.timer = setInterval(() => {
        if (this.selectStatus === 0) {
          this.changeSelectStatus(1);
        } else if (this.selectStatus === 1) {
          this.changeSelectStatus(2);
        } else {
          this.changeSelectStatus(0);
        }
      }, 10 * 1000);
    },
  },
};
</script>
