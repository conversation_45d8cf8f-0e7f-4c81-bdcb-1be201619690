<template>
  <el-row ref="test">
    <el-col :span="24">
      <basic-container>
        <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" :search.sync="query"
          :permission="permissionList" v-model="form" ref="crud" :before-open="beforeOpen" :upload-before="uploadBefore"
          :upload-after="uploadAfter" :upload-delete="uploadDelete" :upload-preview="uploadPreview"
          :upload-exceed="uploadExceed" @row-update="rowUpdate" @search-change="searchChange"
          @search-reset="searchReset" @selection-change="selectionChange" @current-change="currentChange"
          @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad" >
          <!-- 左侧菜单按钮 -->
          <!-- 行操作按钮 -->
          <template slot-scope="{ type, size, row, index }" slot="menu">
            <el-button :type="type" :size="size" @click.stop="$refs.crud.rowView(row, index)">
              <i class="el-icon-view"></i>
              查看
            </el-button>
            <template v-if="row.status !== 2">
              <el-button :type="type" :size="size" @click="handleDeal(row, index)" v-if="row.canDeal">
                <i class="el-icon-bell"></i>
                去处理
              </el-button>
              <el-button :type="type" :size="size" @click="handleOut(row, index)" v-if="row.canTransfer">
                <i class="el-icon-position"></i>
                派发工作
              </el-button>
            </template>
          </template>
          <!-- 派发负责人按钮 -->
          <template slot-scope="{}" slot="distributeForm">
            <el-button size="mini" @click="openGroup()"> 选择人员 </el-button>
          </template>
        </avue-crud>

        <el-dialog title="选择人员" :visible.sync="groupVisible" width="40%" :before-close="() => { groupVisible = false }"
          :append-to-body="true" :close-on-click-modal="false">
          <person-select ref="personSelect" :type="false" @confirm="e => groupConfirm(e)" :existList=existList :isSingle="true" />
        </el-dialog>
      </basic-container>
    </el-col>
  </el-row>
</template>

<script>
import { mapGetters } from "vuex";
import { validateFile } from "@/views/components/util";
// 审核状态
import * as funList from "@/api/digitalGovernance/work.js";
import { downloadFileBlob } from '@/util/util';
import PersonSelect from "./components/personSelect.vue";
export default {
  components: {
    PersonSelect,
  },

  data () {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        updateBtnText: '确 认',
        labelWidth: 120,
        dialogWidth: 1200,
        dialogClickModal: false,
        viewBtn: false,
        delBtn: false, // 默认是有删除、编辑按钮
        editBtn: false,
        dialogCustomClass: "directSupply-dialog",
        column: [
          {
            label: "工作名称",
            prop: "name",
            editDisabled: true,
            searchSpan: 6,
            span: 12,
            search: true,
            maxlength: 100,
            showWordLimit: true,
            rules: [
              {
                required: true,
                whitespace: true,
                message: "请输入标题",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "工作状态",
            prop: "status",
            dataType: "number",
            search: true,
            // editDisabled: true,
            searchSpan: 6,
            span: 12,
            type: "select",
            dicData: [
              { label: "未开始", value: 0 },
              { label: "进行中", value: 1 },
              { label: "已完成", value: 2 },
            ],
            filterable: true,
            rules: [
              {
                required: true,
                message: "请选择工作状态",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "工作类型",
            prop: "type",
            dataType: "string",
            editDisabled: true,
            search: true,
            searchSpan: 6,
            span: 12,
            type: "select",
            dicUrl: `/api/blade-system/dict/dictionary?code=workType`,
            props: {
              //对应select的属性
              label: "dictValue",
              value: "dictKey",
            },
            filterable: true,
            rules: [
              {
                required: true,
                message: "请选择工作状态",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "派发负责人",
            prop: "distribute",
            viewDisplay: false,
            editDisplay: false,
            formslot: true,
            span: 12,
            rules: [{ required: true, message: "请选择负责人" }],
            hide: true,
          },
          // 编辑时表单负责人
          {
            label: "负责人姓名",
            prop: "originUserName",
            editDisabled: true,
            span: 12,
            rules: [{ required: true, message: "请选择负责人" }],
            hide: true,
          },
          {
            label: "时间范围",
            prop: "releaseTime",
            dataType: "array",
            editDisabled: true,
            hide: true,
            slot: true,
            span: 12,
            type: "daterange",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            rules: [
              {
                required: true,
                message: "请选择工作状态",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "实际负责人",
            prop: "userName",
            editDisabled: true,
            editDisplay: true,
            span: 12,
            rules: [{ required: true }],
          },
          {
            label: "工作描述",
            prop: "workDesc",
            type: "textarea",
            hide: true,
            span: 24,
            maxlength: 500,
            editDisabled: true,
            showWordLimit: true,
          },
          {
            label: "执行情况",
            prop: "progress",
            type: "textarea",
            hide: true,
            span: 24,
            maxlength: 500,
            showWordLimit: true,
          },
          {
            label: "备注",
            prop: "remark",
            type: "textarea",
            hide: true,
            span: 24,
            maxlength: 250,
            showWordLimit: true,
          },
          {
            label: "附件",
            prop: "fileList",
            dataType: "object",
            slot: true,
            type: "upload",
            // listType: "picture",
            limit: 10,
            // accept: ".jpeg,.jpg,.png",
            span: 24,
            propsHttp: {
              res: "data",
              url: "link",
              name: "originalName",
            },
            tip: "仅支持上传图片、pdf、office文件,最多只能上传10个文件，单个文件不超过10MB",
            action: "/api/blade-resource/oss/endpoint/put-file-attach",
            hide: true,
            showColumn: false, //不出现显隐面板中
          },
        ],
      },
      data: [],
      groupVisible: false,
      existList: []
    };
  },

  computed: {
    ...mapGetters(["permission", "userInfo"]),
    // 优化后的权限计算属性
    permissionList () {
      const prefix = "mywork";
      return Object.entries({
        addBtn: "_add",
        viewBtn: "_view",
        editBtn: "_edit",
        delBtn: "_delete",
      }).reduce((acc, [key, suffix]) => {
        acc[key] = this.vaildData(this.permission[`${prefix}${suffix}`], false);
        return acc;
      }, {});
    },
    ids () {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created () {
  },
  mounted () {
  },
  methods: {
    groupConfirm (data) {
      this.form.distribute = data.selectUsers // 为了过校验
      this.form.originUserName = data.selectUsersName
      this.groupVisible = false
      this.$nextTick(() => {
        this.$refs.crud.clearValidate(['distribute'])
        this.$refs.crud.clearValidate(['originUserName'])
      })
    },
    openGroup () {
      this.groupVisible = true
      this.$nextTick(() => {
        this.$refs.personSelect.checkedNode = this.existList
      })
    },
    handleOut (row, index) {
      this.$set(this.option, 'editTitle', '工作派发')
      // 修改一些字段不可编辑，负责人改派发负责人
      const userName = this.findObject(this.option.column, "userName");
      userName.editDisplay = false
      const distribute = this.findObject(this.option.column, "distribute");
      distribute.editDisplay = true
      const status = this.findObject(this.option.column, "status");
      status.editDisabled = true
      this.$refs.crud.rowEdit(row, index)
    },
    handleDeal(row, index) {
      this.$set(this.option, 'editTitle', '去处理')
      this.$refs.crud.rowEdit(row, index)
    },
    // 修改
    rowUpdate (row, index, done, loading) {
      if (!this.form.attachList) {
        this.form.attachList = [];
      }
      let submitData = {
        id: row.id,
        status: row.status,
        workDesc: row.workDesc,
        progress: row.progress,
        remark: row.remark,
        attachIds: row.attachList,
        transferUserId: row.distribute
      };
      if (row.releaseTime) {
        if (Array.isArray(row.releaseTime)) {
          submitData.startDate = row.releaseTime[0];
          submitData.endDate = row.releaseTime[1];
        }
      }
      funList.myTransfer(submitData).then(
        async () => {
          this.$message.success(`派发成功`);
          await this.onLoad(this.page, this.query);
          done();
        },
        () => {
          loading();
        }
      );
    },

    // 重置
    searchReset () {
      this.query = {};
      this.onLoad(this.page);
    },
    // 搜索
    searchChange (params, done) {
      this.page.currentPage = 1;
      this.query = params;
      this.onLoad(this.page, params);
      done();
    },

    // 多选删除
    handleDelete () {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return funList.remove({
            ids: this.ids,
          });
        })
        .then(() => {
          this.onLoad(this.page, this.query);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    // 查看预览
    // 打开前回调
    async beforeOpen (done, type) {
      if (["edit", "view"].includes(type)) {
        funList
          .myDetail({
            id: this.form.id,
          })
          .then((res) => {
            this.form = res.data.data;
            this.form.distribute = res.data.data.originUser
            this.form.fileList = [];

            this.form.releaseTime = [this.form.startDate, this.form.endDate];
            if (res.data.data.attachList) {
              let tempAttachList = [];
              res.data.data.attachList.map((value) => {
                this.form.fileList.push({
                  label: value.originalName,
                  value: value.link,
                });
                tempAttachList.push(value.id);
              });
              this.form.attachList = tempAttachList;
            }
            done();
          });
      }
    },
    // 上传前回调
    uploadBefore (file, done, loading, column) {
      // console.log({ file, done, loading, column });
      //文件个数可以在limit属性设置,超过则不会继续上传,也不会走这个函数,这个组件目前只能一次一个个传
      if (validateFile.call(this, "", 10, file)) {
        done();
      } else {
        loading();
      }
    },
    // 上传后执行操作
    uploadAfter (res, done, loading) {
      //fileList是form展示的[{label,value}],attachList是记录每次上传完的id
      if (!res || !res.attachId) {
        this.$message.error("上传失败");
        loading();
      } else {
        this.$message.success("上传成功");

        if (!this.form.attachList) {
          this.form.attachList = [];
          this.form.attachList.push(res.attachId);
        } else {
          this.form.attachList.push(res.attachId);
        }
        done();
      }
    },
    // 删除已上传文件
    uploadDelete (file, column) {
      // console.log({ file, column });

      return this.$confirm("是否确定移除该项？").then(() => {
        if (column.prop == "themePictureList") {
          this.form.themePicture = "";
          this.form.themePictureList.splice(file.uid, 1);
        } else {
          this.form.fileList.splice(file.uid, 1);
          this.form.attachList.splice(file.uid, 1);
        }
      });
    },
    // 上传限制
    uploadExceed (limit) {
      this.$message.error(`最多只能上传${limit}个文件`);
    },
    uploadPreview (file, column, done) {
      // console.log({ file, column, done });
      // done()
      if (file.type == "img") {
        done()
      } else {
        //url下载
        downloadFileBlob(file.url, file.name)
      }
    },
    // 当前页切换
    currentChange (currentPage) {
      this.page.currentPage = currentPage;
    },
    // 页面显示条数切换
    sizeChange (pageSize) {
      this.page.pageSize = pageSize;
    },
    // 刷新
    refreshChange () {
      this.onLoad(this.page, this.query);
    },
    // 首次加载
    async onLoad (page, params = {}) {
      this.loading = true;
      let query = {
        ...params,
        current: this.page.currentPage,
        size: this.page.pageSize,
      };

      let res = await funList.getMyList(query);
      if (res && res.data && res.data.success) {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        if (this.page.currentPage > 1 && this.page.currentPage > data.pages) {
          this.page.currentPage = data.pages
          this.onLoad()
        }
      }
    },


  },
};
</script>
<style scoped lang="scss">
.img-tiny {
  height: 100px;
  width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(245, 247, 250);
}

.btn-group-container {
  display: flex;
}

.user-list-container {
  display: flex;
  flex-direction: column;
  height: 400px;
}

.tab-container {
  display: flex;
  justify-content: space-around;
  padding: 10px;
  background-color: #f0f0f0;
}

.tab {
  cursor: pointer;
  padding: 5px 10px;
}

.tab.active {
  border-bottom: 2px solid #409eff;
  color: #409eff;
}

.user-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.user-item {
  padding: 10px;
  border-bottom: 1px solid #ddd;
  cursor: pointer;
}

.user-item:hover {
  background-color: #409eff;
  /* 鼠标悬停时的背景色 */
  color: white;
}

.loading {
  text-align: center;
  padding: 10px;
  color: #999;
}
</style>
