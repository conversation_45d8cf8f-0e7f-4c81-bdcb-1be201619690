<!--
 * @Description: 左上-领导班子
 * @Author: chenz76
 * @Date: 2021-10-26 14:42:02
 * @LastEditors: chenz76
 * @LastEditTime: 2023-06-06 09:52:16
-->
<template>
  <div style="flex: 0 0 100%; position: relative; display: flex; flex-direction: column; padding: 1vh 1vw; box-sizing: border-box;" :class="{ 'rightP': isLeft, 'leftP': !isLeft }">
    <div style="flex: 0 0 100%; position: relative; display: flex; flex-direction: column;">
      <img class="img-common" src="/img/bigScreen/modulebg.png" mode="scaleToFill" />

      <HeaderView :idx="idx" :title="title"></HeaderView>

      <div class="foot-grid" v-if="hasMenu">
        <SparkGridItem v-for="(item, index) in dataList" :key="index" :data="item"></SparkGridItem>
      </div>
      <div  style="position: relative; flex:1; box-sizing: border-box; display: flex; justify-content: center; align-items: center;">
        <img style="position: absolute; top: 4%; left: 1%; width: 100%; height: 25px; z-index: -1" src="/img/bigScreen/scrollHead.png" mode="scaleToFill">
        <dv-scroll-board style="width: 92%; height: 92%;" :config="config" ref="scrollBoard" />
      </div>
    </div>
  </div>
</template>
<script>
import HeaderView from '../common/headerView.vue';
import SparkGridItem from "../common/sparkGridItem.vue";

// import { ScrollBoard } from '@jiaminghi/data-view'
export default {
  name: 'WarnView',
  components: {
    HeaderView,
    SparkGridItem,
    // ScrollBoard,
  },
  props: {
    idx: {
      type: String,
      default: "02",
    },
    title: {
      type: String,
      default: "防溺水预警",
    },
    isLeft: {
      type: Boolean,
      default: true,
    },
    hasMenu: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    const debugData = [

          ['2025-07-15 13:00:12', '水库摄像头', '人员靠近', '已处理'],
          ['2025-07-15 13:00:12', '水库摄像头', '人员靠近', '已处理'],
          ['2025-07-15 13:00:12', '水库摄像头', '人员靠近', '已处理'],
          ['2025-07-15 13:00:12', '水库摄像头', '人员靠近', '已处理'],
          ['2025-07-15 13:00:12', '水库摄像头', '人员靠近', '已处理'],
          ['2025-07-15 13:00:12', '水库摄像头', '人员靠近', '已处理'],
          ['2025-07-15 13:00:12', '水库摄像头', '人员靠近', '已处理'],
          ['2025-07-15 13:00:12', '水库摄像头', '人员靠近', '已处理'],
          ['2025-07-15 13:00:12', '水库摄像头', '人员靠近', '已处理'],
          ['2025-07-15 13:00:12', '水库摄像头', '人员靠近', '已处理'],
          ['2025-07-15 13:00:12', '水库摄像头', '人员靠近', '已处理']
    ]
    const screeW = document.body.clientWidth
    return {
      dataList: [
        {
          label: '随手拍',
          value: '720',
          valueKey: '',
          unit: '个',
        },
        {
          label: '随手拍',
          value: '720',
          valueKey: '',
          unit: '个',
        },
        {
          label: '随手拍',
          value: '720',
          valueKey: '',
          unit: '个',
        },

      ],
      config: {
        header: [
          "时间",
          "摄像头",
          "预警行为",
          "处理状态",
        ],
        rowNum: 6,
        headerBGC: "#11397f00",
        headerHeight: 25,
        // columnWidth: [100, 120, 80, 80],
        // columnWidth: ['25%', '25%', '30%', '20%'],
        columnWidth: [114],
        // columnWidth: [170 * screen.width / 1920, 110 * screen.width / 1920, 100 * screen.width / 1920, 100 * screen.width / 1920],
        evenRowBGC: "#09245b",
        oddRowBGC: "linear-gradient(90deg, #0073fe40 -41.43%, #0073fe00 96.18%) !important;",
        // evenRowBGC: "#09245b",
        // oddRowBGC: "#0073fe40",
        data: debugData,
        align: ['center','center','center','center'],
      },
    };
  },
  created() {
    console.log('screen.width=======', screen.width)
    this.setResize();
  },
  methods: {
    setResize() {
      let self = this;
      window.addEventListener(
        "resize",
        // debounce(function () {
        //   if (self.myChart) self.myChart.resize();
        // }, 200)
      );
    },
  },
};
</script>

<style lang="scss" scoped>

@import "../common/common.scss";

.leftP {
  padding-left: 0.5vw !important;
}

.rightP {
  padding-right: 0.5vw !important;
}

.foot-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr); // 4列
  grid-template-rows: repeat(1, 1fr);    // 2行
  gap: 0 2vw;                          // 行间距和列间距
  padding: 1.5vh 1.14vw;
}

// 自定义 dv-scroll-board 样式
::v-deep .dv-scroll-board {
  .header {
    // height: 2.96vh !important;
    font-size: 1.3vh !important;
    color: #ffffff !important;
    font-weight: regular;
    // align-items: center;
    // justify-content: center;
  }

  // .row-item {
  //   height: 24px !important;
  // }

  .ceil {
    font-size: 1.2vh !important;
    color: #ffffff !important;
    // padding: 0.42vh 0;
  }
}
</style>
