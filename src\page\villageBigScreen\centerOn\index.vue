<!--
 * @Description: 中上-乡村概况
 * @Author: chenz76
 * @Date: 2021-10-26 14:42:02
 * @LastEditors: chenn26
 * @LastEditTime: 2023-01-18 11:57:15
-->
<template>
  <div style="position: relative; display: flex; flex-direction: column; padding: 1vh 0.5vw; box-sizing: border-box; width: 100%; height: 100%;">
    <div style="flex: 0 0 100%; position: relative; display: flex; flex-direction: column; box-sizing: border-box">
      <img class="img-common" src="/img/bigScreen/mapBg.png" mode="scaleToFill" />
      <div style="flex:1; display: flex; justify-content: center; align-items: center; padding: 7.1vh 10.9vh 4.6vw;">
        <!-- <img style="width: 34.6vw; height: 69vh;" src="/img/bigScreen/map.png" mode="scaleToFill" /> -->
        <!-- <img style="width: 100%; height: 100%;" src="/img/bigScreen/map.png" mode="scaleToFill" /> -->
        <div ref="mapChart" style="width: 100%; height: 100%;"></div>
      </div>
    </div>

    <!-- <div style="position: absolute; width: 100%; bottom: 20vh; left: 0; right: 0; display: flex; flex-shrink: 0; flex-grow: 0;">
      <CenterFlow></CenterFlow>
    </div> -->
  </div>
</template>
<script>
import CenterFlow from "../centerFlow/index.vue";
import * as echarts from 'echarts';
import villageJson from "./village";
import fujianJson from "./fujian";
import tdTheme from "./theme.json"; // 引入默认主题
export default {
  components: {
    CenterFlow,
  },

  props: {
    villageDesc: String,
    descImageList: Array,
    villageName: String,
    isShow: Boolean
  },
  data() {
    return {
      chart: null,
    };
  },
  mounted() {
    echarts.registerTheme("tdTheme", tdTheme); // 覆盖默认主题
    echarts.registerMap("village", villageJson);
    this.initChart();
  },
  // filters: {
  //   filter_name(value) {
  //     if (value.indexOf("委会") != -1) {
  //       return value.slice(0, -2)
  //     } else {
  //       return value
  //     }
  //   },
  // },
  destroyed() {
    if (this.chart) {
      // 重要  释放之前的图表实例， 否则改变的主题无效果
      this.chart.dispose();
      this.chart = null;
    }
  },
  methods: {
    initChart() {
      const chartDom = this.$refs.mapChart;
      this.chart = echarts.init(chartDom);


      const option = {
        showLegendSymbol: true,
        // backgroundColor: '#072f5f',
        tooltip: {
          trigger: "item",
          position: (point) => {
            // 固定在顶部
            return [point[0] + 50, point[1] - 20];
          },
          // 如果需要自定义 tooltip样式，需要使用formatter
          // formatter: (params) => {
          //   const data = params.data.elseData;
          //   if (data) {
          //     return `<div  style="text-align:left">${data.cityDimension}<br/>部署量: ${data.deployNum} 个<br/>累计用户: ${data.userNum} 人<br/>累计激活: ${data.activeNum} 人<br/>月活跃量: ${data.enlivenNum} 人</div>`;
          //   } else {
          //     return ``;
          //   }
          // },
          textStyle: {
            align: "left",
            color: "#FFF",
            fontSize: "16",
          },
          backgroundColor: "rgba(15, 52, 135, 0.7)",
          borderWidth: "1",
          borderColor: "#5cc1ff",
          extraCssText: "box-shadow: 0 0 10px rgba(255, 255, 255, 0.7);",
        },
        visualMap: {
          min: 0,
          max: 10,
          show: false,
          seriesIndex: 0,
          // 颜色
          inRange: {
            color: ["rgba(41,166,206, .5)", "rgba(69,117,245, .9)"],
          },
        },
        // 底部背景
        geo: {
          show: true,
          aspectScale: 0.9, //长宽比
          zoom: 1.2,
          top: "10%",
          left: "16%",
          map: "village",
          roam: false,
          itemStyle: {
            normal: {
              areaColor: "rgba(0,0,0,0)",
              shadowColor: "rgba(7,114,204, .8)",
              shadowOffsetX: 5,
              shadowOffsetY: 5,
            },
            emphasis: {
              areaColor: "#00aeef",
            },
          },
        },
        series: [
          {
            name: "相关指数",
            type: "map",
            aspectScale: 0.85, //长宽比
            zoom: 1.2,
            map: "village", // 自定义扩展图表类型
            top: "10%",
            left: "16%",
            itemStyle: {
              normal: {
                color: "red",
                areaColor: "rgba(19,54,162, .5)",
                borderColor: "rgba(0,242,252,.3)",
                borderWidth: 1,
                shadowBlur: 7,
                shadowColor: "#00f2fc",
              },
              emphasis: {
                areaColor: "#4f7fff",
                borderColor: "rgba(0,242,252,.6)",
                borderWidth: 2,
                shadowBlur: 10,
                shadowColor: "#00f2fc",
              },
            },
            label: {
              formatter: (params) => `${params.name}`,
              show: true,
              position: "insideRight",
              textStyle: {
                fontSize: 14,
                color: "#efefef",
              },
              emphasis: {
                textStyle: {
                  color: "#fff",
                },
              },
            },
            data: [],
          },
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            data: [
              // 添加更多标记点
            ],
            symbolSize: function (val) {
              return val[2] * 10;
            },
            showEffectOn: 'render',
            rippleEffect: {
              brushType: 'stroke'
            },
            hoverAnimation: true,
            label: {
              formatter: '{b}',
              position: 'right',
              show: true
            },
            itemStyle: {
              color: 'orange' // 标记颜色
            },
            zlevel: 1
          }
        ]
      };


      this.chart.setOption(option, true);

    }
  }
};
</script>

<style lang="scss" scoped>

.img-common {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}
</style>
