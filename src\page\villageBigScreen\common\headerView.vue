<!--
 * @Description: 右下-民情统计、信箱统计
 * @Author: chenz76
 * @Date: 2021-10-26 14:42:02
 * @LastEditors: chenz76
 * @LastEditTime: 2022-01-06 10:38:58
-->
<template>
  <div style="display: flex; flex-direction: row; padding-right: 1vw;">
    <div class="head-idx" style="position: relative; width: 3vh; height: 3vh; font-weight: bold;">
      <img class="img-common" src="/img/bigScreen/idx.png" mode="scaleToFill" />
      {{ idx }}
    </div>
    <div style="position: relative; width: 100%; height: 100%; display: flex; align-items: center;">
      <img class="img-common" src="/img/bigScreen/subTitlebg.png" mode="scaleToFill" />
      <div class="head-title" style="margin-left: 1vw;">{{ title }}</div>
    </div>
  </div>
</template>
<script>
export default {
  name: "HeaderView",
  props: {
    idx: { type: String, default: "01" },
    title: String,
  },
  data() {
    return {
    };
  },
  computed: {},
  mounted() {
  },
  created() {
  },
  destroyed() {
  },
  methods: {
  },
};
</script>

<style lang="scss" scoped>

@import './common.scss';

.head-idx {
  text-shadow: 0 4px 8px #00000040, 0 8px 16px #00000040;
  font-size: 1.7vh;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  background: linear-gradient(188deg, #C9F5FF 11.62%, #00FFFC 30.26%, #00A1FF 94.12%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
}

.head-title {
  text-shadow: 0 4px 8px #00000040, 0 8px 16px #00000040;
  font-size: 1.5vh;
  font-style: normal;
  font-weight: Bold;
  line-height: normal;
  background: linear-gradient(188deg, #C9F5FF 11.62%, #00FFFC 30.26%, #00A1FF 94.12%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: flex;
  align-items: center;
}
</style>
