<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page.sync="page"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList,getDetail} from "@/api/system/track";

  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: false,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          height: 'auto',
          calcHeight: 30,
          tip: false,
          searchShow: true,
          searchMenuSpan: 6,
          border: true,
          index: true,
          addBtn: false,
          viewBtn: true,
          editBtn: false,
          delBtn: false,
          selection: false,
          menu:false,
          dialogClickModal: false,
          column: [
            {
              label: "账号",
              prop: "account",
            },
            {
              label: "用户",
              prop: "realName",
            },
            {
              label: "页面标题",
              prop: "pageTitle",
              search: true
            },
            {
              label: "页面URL",
              prop: "pageUrl",
            },
            {
              label: "平台",
              prop: "platform",
              type: "select",
              dicData: [
                {
                  label: "WEB",
                  value: 1
                },
                {
                  label: "小程序",
                  value: 2
                }
              ],
              // search: true
            },
            {
              label: "模块",
              prop: "module",
              type: "select",
              dicUrl: "/api/blade-system/dict/dictionary?code=module",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              // dataType: "number",
              // slot: true,
              search: true,
            },
            {
              label: "用户行为",
              prop: "action",
              type: "select",
              dicUrl: "/api/blade-system/dict/dictionary?code=action",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              // dataType: "number",
              // slot: true,
              search: true,
            },
            {
              label: "上报时间",
              prop: "trackTime",
              formatter: (row) => {
                const date = new Date(row.trackTime);
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                const seconds = String(date.getSeconds()).padStart(2, '0');
                
                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
              }
            },
            {
              label: "业务主ID",
              prop: "businessId",
              hide: true,
            },
            {
              label: "子ID",
              prop: "subId",
              hide: true,
            },
          ]
        },
        data: []
      };
    },
    computed: {
    },
    methods: {
      
  
      beforeOpen(done, type) {
        if (["view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage) {
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize) {
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
