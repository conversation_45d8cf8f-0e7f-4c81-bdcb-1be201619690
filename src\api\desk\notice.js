import request from '@/router/axios';


/**
 * @description: 请求列表
 * @param {object} params
 * @author:
 */
export const getList = (params) => {
  return request({
    url: '/api/admin/customNotice/page',
    method: 'get',
    params,
  })
}

/**
 * @description: 新增
 * @param {object} data
 * @author:
 */
export const save = (data) => {
  return request({
    url: '/api/admin/customNotice/save',
    method: 'post',
    data,
  })
}

/**
 * @description: 查看详情
 * @param {object} params
 * @author:
 */
export const detail = (params) => {
  return request({
    url: '/api/admin/customNotice/detail',
    method: 'get',
    params,
  })
}

/**
 * @description: 修改
 * @param {object} data
 * @author:
 */
export const update = (data) => {
  return request({
    url: '/api/admin/customNotice/update',
    method: 'post',
    data,
  })
}



/**
 * @description: 删除
 * @param {object} params
 * @author:
 */
export const remove = (params) => {
  return request({
    url: '/api/admin/customNotice/remove',
    method: 'post',
    params,
  })
}


/**
 * @description: 发布
 * @param {object} params
 * @author:
 */
 export const release = (params) => {
  return request({
    url: '/api/admin/customNotice/release',
    method: 'post',
    params,
  })
}

/**
 * @description: 取消发布
 * @param {object} params
 * @author:
 */
export const unRelease = (params) => {
  return request({
    url: '/api/admin/customNotice/unRelease',
    method: 'post',
    params,
  })
}

export const getUserList = (current, size, params) => {
  return request({
      url: '/api/user/customNotice/page',
      method: 'get',
      params: {
        platform:2,
        ...params,
        current,
        size,
      }
    })
}

export const userRead = (ids) => {
  return request({
    url: '/api/user/customNotice/doRead',
    method: 'post',
    params:{
      ids,
    }
  })
}
